package model

import "context"

type StockAlertUsecase interface {
	Create(ctx context.Context, stockAlert StockAlertCreate) (string, error)
	Update(ctx context.Context, stockAlert StockAlertUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*StockAlert, error)
	GetAll(ctx context.Context) ([]StockAlertWithDetails, error)
	GetAllWithDetails(ctx context.Context) ([]StockAlertWithDetails, error)
	GetByPropWithDetails(ctx context.Context, prop string, value string) (*StockAlertWithDetails, error)
	GetByInventoryID(ctx context.Context, inventoryID string) ([]StockAlertWithDetails, error)
	GetByStatus(ctx context.Context, status string) ([]StockAlertWithDetails, error)
	GetByAlertType(ctx context.Context, alertType string) ([]StockAlertWithDetails, error)
	GetActiveAlerts(ctx context.Context) ([]Stock<PERSON>lertWithDetails, error)
	GetActiveAlertsByInventoryID(ctx context.Context, inventoryID string) ([]StockAlertWithDetails, error)
	ResolveAlert(ctx context.Context, id string) error
	DismissAlert(ctx context.Context, id string) error
	Delete(ctx context.Context, id string) error
	ValidateAlertType(ctx context.Context, alertType string) error
	ValidateStatus(ctx context.Context, status string) error
}
