package model

import "context"

type StockAlertRepository interface {
	Create(ctx context.Context, stockAlert StockAlert) error
	Update(ctx context.Context, stockAlert StockAlert) error
	GetByProp(ctx context.Context, prop string, value string) (*StockAlert, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]StockAlertWithDetails, error)
	GetAllWithDetails(ctx context.Context) ([]StockAlertWithDetails, error)
	GetByPropWithDetails(ctx context.Context, prop string, value string) (*StockAlertWithDetails, error)
	GetByInventoryID(ctx context.Context, inventoryID string) ([]StockAlertWithDetails, error)
	GetByStatus(ctx context.Context, status string) ([]StockAlertWithDetails, error)
	GetByAlertType(ctx context.Context, alertType string) ([]StockAlertWithDetails, error)
	GetActiveAlerts(ctx context.Context) ([]StockAlertWithDetails, error)
	GetActiveAlertsByInventoryID(ctx context.Context, inventoryID string) ([]StockAlertWithDetails, error)
	Delete(ctx context.Context, id string) error
}
