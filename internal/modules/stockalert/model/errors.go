package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	StockAlertConflictCode        utils.ErrCode = utils.StockAlertCode + iota
	StockAlertNotFoundCode
	StockAlertValidationErrorCode
	StockAlertInvalidStatusCode
	StockAlertInvalidTypeCode
	StockAlertAlreadyResolvedCode
)

func StockAlertConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockAlertConflictCode, message, err, details)
}

func StockAlertNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockAlertNotFoundCode, message, err, details)
}

func StockAlertValidationErrorf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockAlertValidationErrorCode, message, err, details)
}

func StockAlertInvalidStatusf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockAlertInvalidStatusCode, message, err, details)
}

func StockAlertInvalidTypef(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockAlertInvalidTypeCode, message, err, details)
}

func StockAlertAlreadyResolvedf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockAlertAlreadyResolvedCode, message, err, details)
}
