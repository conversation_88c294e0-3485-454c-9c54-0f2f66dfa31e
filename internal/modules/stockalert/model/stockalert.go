package model

import (
	"time"

	inventoryModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
)

type StockAlert struct {
	ID          string
	InventoryID string
	AlertType   string // LOW_STOCK, EXPIRED, OVERSTOCK
	Status      string // ACTIVE, RESOLVED, DISMISSED
	TriggeredAt *time.Time
	ResolvedAt  *time.Time
	CreatedAt   *time.Time
	UpdatedAt   *time.Time
	DeletedAt   *time.Time
}

type StockAlertCreate struct {
	InventoryID string
	AlertType   string
	Status      string
	TriggeredAt *time.Time
}

type StockAlertUpdate struct {
	ID          string
	InventoryID string
	AlertType   string
	Status      string
	TriggeredAt *time.Time
	ResolvedAt  *time.Time
}

// StockAlertWithDetails includes complete information about the related inventory
type StockAlertWithDetails struct {
	ID          string
	InventoryID string
	AlertType   string
	Status      string
	TriggeredAt *time.Time
	ResolvedAt  *time.Time
	CreatedAt   *time.Time
	UpdatedAt   *time.Time
	DeletedAt   *time.Time
	// Related entities
	Inventory inventoryModel.InventoryWithDetails
}

// Alert type constants
const (
	AlertTypeLowStock  = "LOW_STOCK"
	AlertTypeExpired   = "EXPIRED"
	AlertTypeOverstock = "OVERSTOCK"
)

// Alert status constants
const (
	StatusActive    = "ACTIVE"
	StatusResolved  = "RESOLVED"
	StatusDismissed = "DISMISSED"
)
