package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/repo/pg"
)

type stockAlertRepository struct {
	pgRepo pg.StockAlertPostgreRepo
}

// CountByProp implements model.StockAlertRepository.
func (s *stockAlertRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return s.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.StockAlertRepository.
func (s *stockAlertRepository) Create(ctx context.Context, stockAlert model.StockAlert) error {
	return s.pgRepo.Create(ctx, stockAlert)
}

// Delete implements model.StockAlertRepository.
func (s *stockAlertRepository) Delete(ctx context.Context, id string) error {
	return s.pgRepo.Delete(ctx, id)
}

// GetAll implements model.StockAlertRepository.
func (s *stockAlertRepository) GetAll(ctx context.Context) ([]model.StockAlertWithDetails, error) {
	return s.pgRepo.GetAll(ctx)
}

// GetAllWithDetails implements model.StockAlertRepository.
func (s *stockAlertRepository) GetAllWithDetails(ctx context.Context) ([]model.StockAlertWithDetails, error) {
	return s.pgRepo.GetAllWithDetails(ctx)
}

// GetByProp implements model.StockAlertRepository.
func (s *stockAlertRepository) GetByProp(ctx context.Context, prop string, value string) (*model.StockAlert, error) {
	return s.pgRepo.GetByProp(ctx, prop, value)
}

// GetByPropWithDetails implements model.StockAlertRepository.
func (s *stockAlertRepository) GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockAlertWithDetails, error) {
	return s.pgRepo.GetByPropWithDetails(ctx, prop, value)
}

// GetByInventoryID implements model.StockAlertRepository.
func (s *stockAlertRepository) GetByInventoryID(ctx context.Context, inventoryID string) ([]model.StockAlertWithDetails, error) {
	return s.pgRepo.GetByInventoryID(ctx, inventoryID)
}

// GetByStatus implements model.StockAlertRepository.
func (s *stockAlertRepository) GetByStatus(ctx context.Context, status string) ([]model.StockAlertWithDetails, error) {
	return s.pgRepo.GetByStatus(ctx, status)
}

// GetByAlertType implements model.StockAlertRepository.
func (s *stockAlertRepository) GetByAlertType(ctx context.Context, alertType string) ([]model.StockAlertWithDetails, error) {
	return s.pgRepo.GetByAlertType(ctx, alertType)
}

// GetActiveAlerts implements model.StockAlertRepository.
func (s *stockAlertRepository) GetActiveAlerts(ctx context.Context) ([]model.StockAlertWithDetails, error) {
	return s.pgRepo.GetActiveAlerts(ctx)
}

// GetActiveAlertsByInventoryID implements model.StockAlertRepository.
func (s *stockAlertRepository) GetActiveAlertsByInventoryID(ctx context.Context, inventoryID string) ([]model.StockAlertWithDetails, error) {
	return s.pgRepo.GetActiveAlertsByInventoryID(ctx, inventoryID)
}

// Update implements model.StockAlertRepository.
func (s *stockAlertRepository) Update(ctx context.Context, stockAlert model.StockAlert) error {
	return s.pgRepo.Update(ctx, stockAlert)
}

func NewStockAlertRepository(pgRepo pg.StockAlertPostgreRepo) model.StockAlertRepository {
	return &stockAlertRepository{
		pgRepo: pgRepo,
	}
}
