package pg

import (
	"context"
	"fmt"

	brandModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
	inventoryModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
	warehouseModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (s *stockAlertPostgreRepo) GetAllWithDetails(ctx context.Context) ([]model.StockAlertWithDetails, error) {
	var stockAlerts []model.StockAlertWithDetails

	err := pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT 
				sa.id, sa.inventory_id, sa.alert_type, sa.status, sa.triggered_at,
				sa.resolved_at, sa.created_at, sa.updated_at, sa.deleted_at,
				-- Inventory information
				i.warehouse_id, i.product_id, i.batch_code, i.expiration_date,
				i.current_stock, i.reserved_stock, i.available_stock, i.min_stock,
				i.max_stock, i.reorder_point, i.last_movement_date,
				i.created_at, i.updated_at, i.deleted_at,
				-- Product information
				p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
				p.measurement_unit_id, p.state, p.description, p.can_be_sold,
				p.can_be_purchased, p.cost_price, p.cost_price_total,
				p.created_at, p.updated_at, p.deleted_at,
				-- Brand information
				b.id, b.name, b.code, b.created_at, b.updated_at, b.deleted_at,
				-- Warehouse information
				w.name, w.code, w.type, w.category, w.description, w.address,
				w.is_active, w.is_system_warehouse, w.created_at, w.updated_at, w.deleted_at,
				-- Category IDs array
				COALESCE(ARRAY_AGG(pc.category_id ORDER BY pc.created_at ASC) FILTER (WHERE pc.category_id IS NOT NULL), '{}') as category_ids
			FROM stock_alerts sa
			JOIN inventory i ON sa.inventory_id = i.id
			JOIN products p ON i.product_id = p.id
			JOIN brands b ON p.brand_id = b.id
			JOIN warehouses w ON i.warehouse_id = w.id
			LEFT JOIN product_categories pc ON p.id = pc.product_id
			WHERE sa.deleted_at IS NULL 
			  AND i.deleted_at IS NULL 
			  AND p.deleted_at IS NULL 
			  AND b.deleted_at IS NULL 
			  AND w.deleted_at IS NULL
			GROUP BY 
				sa.id, sa.inventory_id, sa.alert_type, sa.status, sa.triggered_at,
				sa.resolved_at, sa.created_at, sa.updated_at, sa.deleted_at,
				i.id, i.warehouse_id, i.product_id, i.batch_code, i.expiration_date,
				i.current_stock, i.reserved_stock, i.available_stock, i.min_stock,
				i.max_stock, i.reorder_point, i.last_movement_date, i.created_at,
				i.updated_at, i.deleted_at,
				p.id, p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
				p.measurement_unit_id, p.state, p.description, p.can_be_sold,
				p.can_be_purchased, p.cost_price, p.cost_price_total,
				p.created_at, p.updated_at, p.deleted_at,
				b.id, b.name, b.code, b.created_at, b.updated_at, b.deleted_at,
				w.id, w.name, w.code, w.type, w.category, w.description, w.address,
				w.is_active, w.is_system_warehouse, w.created_at, w.updated_at, w.deleted_at
			ORDER BY sa.created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get all stock alerts with details", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var stockAlert model.StockAlertWithDetails
			var inventory inventoryModel.InventoryWithDetails
			var product productModel.Product
			var brand brandModel.Brand
			var warehouse warehouseModel.Warehouse

			err := rows.Scan(
				// StockAlert fields
				&stockAlert.ID, &stockAlert.InventoryID, &stockAlert.AlertType,
				&stockAlert.Status, &stockAlert.TriggeredAt, &stockAlert.ResolvedAt,
				&stockAlert.CreatedAt, &stockAlert.UpdatedAt, &stockAlert.DeletedAt,
				// Inventory fields
				&inventory.WarehouseID, &inventory.ProductID, &inventory.BatchCode,
				&inventory.ExpirationDate, &inventory.CurrentStock, &inventory.ReservedStock,
				&inventory.AvailableStock, &inventory.MinStock, &inventory.MaxStock,
				&inventory.ReorderPoint, &inventory.LastMovementDate,
				&inventory.CreatedAt, &inventory.UpdatedAt, &inventory.DeletedAt,
				// Product fields
				&product.Name, &product.ImageURL, &product.CommercialName,
				&product.Code, &product.SKUCode, &product.MeasurementUnitID,
				&product.State, &product.Description, &product.CanBeSold,
				&product.CanBePurchased, &product.CostPrice, &product.CostPriceTotal,
				&product.CreatedAt, &product.UpdatedAt, &product.DeletedAt,
				// Brand fields
				&brand.ID, &brand.Name, &brand.Code,
				&brand.CreatedAt, &brand.UpdatedAt, &brand.DeletedAt,
				// Warehouse fields
				&warehouse.Name, &warehouse.Code, &warehouse.Type,
				&warehouse.Category, &warehouse.Description, &warehouse.Address,
				&warehouse.IsActive, &warehouse.IsSystemWarehouse,
				&warehouse.CreatedAt, &warehouse.UpdatedAt, &warehouse.DeletedAt,
				// Category IDs
				&product.CategoryIDs,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan stock alert with details", err, nil)
			}

			// Set IDs for related entities
			inventory.ID = stockAlert.InventoryID
			product.ID = inventory.ProductID
			product.BrandID = brand.ID
			warehouse.ID = inventory.WarehouseID

			inventory.Product = product
			inventory.Brand = brand
			inventory.Warehouse = warehouse

			stockAlert.Inventory = inventory

			stockAlerts = append(stockAlerts, stockAlert)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return stockAlerts, nil
}

// GetAll is an alias for GetAllWithDetails to maintain interface compatibility
func (s *stockAlertPostgreRepo) GetAll(ctx context.Context) ([]model.StockAlertWithDetails, error) {
	return s.GetAllWithDetails(ctx)
}

// GetByPropWithDetails implements StockAlertPostgreRepo.
func (s *stockAlertPostgreRepo) GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockAlertWithDetails, error) {
	stockAlerts, err := s.getStockAlertsByFilter(ctx, fmt.Sprintf("sa.%s = $1", prop), value)
	if err != nil {
		return nil, err
	}

	if len(stockAlerts) == 0 {
		return nil, nil
	}

	return &stockAlerts[0], nil
}
