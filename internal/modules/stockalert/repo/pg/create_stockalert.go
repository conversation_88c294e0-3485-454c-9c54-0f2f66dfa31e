package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
)

// Create implements StockAlertPostgreRepo.
func (s *stockAlertPostgreRepo) Create(ctx context.Context, stockAlert model.StockAlert) error {
	query := `
		INSERT INTO dev.stock_alerts (
			id, inventory_id, alert_type, status, triggered_at, resolved_at,
			created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8
		)`

	_, err := s.pool.Exec(ctx, query,
		stockAlert.ID,
		stockAlert.InventoryID,
		stockAlert.AlertType,
		stockAlert.Status,
		stockAlert.TriggeredAt,
		stockAlert.ResolvedAt,
		stockAlert.CreatedAt,
		stockAlert.UpdatedAt,
	)

	return err
}
