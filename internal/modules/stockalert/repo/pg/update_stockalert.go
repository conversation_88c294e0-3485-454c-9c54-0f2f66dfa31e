package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
)

// Update implements StockAlertPostgreRepo.
func (s *stockAlertPostgreRepo) Update(ctx context.Context, stockAlert model.StockAlert) error {
	query := `
		UPDATE dev.stock_alerts 
		SET 
			inventory_id = $2,
			alert_type = $3,
			status = $4,
			triggered_at = $5,
			resolved_at = $6,
			updated_at = $7
		WHERE id = $1 AND deleted_at IS NULL`

	_, err := s.pool.Exec(ctx, query,
		stockAlert.ID,
		stockAlert.InventoryID,
		stockAlert.AlertType,
		stockAlert.Status,
		stockAlert.TriggeredAt,
		stockAlert.ResolvedAt,
		stockAlert.UpdatedAt,
	)

	return err
}
