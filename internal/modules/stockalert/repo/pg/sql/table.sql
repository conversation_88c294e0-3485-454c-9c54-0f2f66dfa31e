CREATE TABLE dev.stock_alerts (
    id VARCHAR(255) PRIMARY KEY,
    inventory_id VARCHAR(255) NOT NULL,
    alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('LOW_STOCK', 'EXPIRED', 'OVERSTOCK')),
    status VARCHAR(50) NOT NULL CHECK (status IN ('ACTIVE', 'RESOLVED', 'DISMISSED')),
    triggered_at TIMESTAMPTZ,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (inventory_id) REFERENCES dev.inventory(id)
);

-- Index for better performance on common queries
CREATE INDEX idx_stock_alerts_inventory_id ON dev.stock_alerts(inventory_id);
CREATE INDEX idx_stock_alerts_alert_type ON dev.stock_alerts(alert_type);
CREATE INDEX idx_stock_alerts_status ON dev.stock_alerts(status);
CREATE INDEX idx_stock_alerts_triggered_at ON dev.stock_alerts(triggered_at);
CREATE INDEX idx_stock_alerts_resolved_at ON dev.stock_alerts(resolved_at);
CREATE INDEX idx_stock_alerts_created_at ON dev.stock_alerts(created_at);

-- Composite index for active alerts by inventory
CREATE INDEX idx_stock_alerts_active_inventory ON dev.stock_alerts(inventory_id, status) WHERE status = 'ACTIVE';

-- Composite index for active alerts by type
CREATE INDEX idx_stock_alerts_active_type ON dev.stock_alerts(alert_type, status) WHERE status = 'ACTIVE';
