package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type StockAlertPostgreRepo interface {
	Create(ctx context.Context, stockAlert model.StockAlert) error
	Update(ctx context.Context, stockAlert model.StockAlert) error
	GetByProp(ctx context.Context, prop string, value string) (*model.StockAlert, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.StockAlertWithDetails, error)
	GetAllWithDetails(ctx context.Context) ([]model.StockAlertWithDetails, error)
	GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockAlertWithDetails, error)
	GetByInventoryID(ctx context.Context, inventoryID string) ([]model.StockAlertWithDetails, error)
	GetByStatus(ctx context.Context, status string) ([]model.StockAlertWithDetails, error)
	GetByAlertType(ctx context.Context, alertType string) ([]model.StockAlertWithDetails, error)
	GetActiveAlerts(ctx context.Context) ([]model.StockAlertWithDetails, error)
	GetActiveAlertsByInventoryID(ctx context.Context, inventoryID string) ([]model.StockAlertWithDetails, error)
	Delete(ctx context.Context, id string) error
}

type stockAlertPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewStockAlertPostgreRepo(pool *pgxpool.Pool) StockAlertPostgreRepo {
	return &stockAlertPostgreRepo{
		pool: pool,
	}
}
