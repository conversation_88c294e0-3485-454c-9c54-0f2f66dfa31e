package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
	"github.com/jackc/pgx/v5"
)

// GetByProp implements StockAlertPostgreRepo.
func (s *stockAlertPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.StockAlert, error) {
	query := fmt.Sprintf(`
		SELECT 
			id, inventory_id, alert_type, status, triggered_at, resolved_at,
			created_at, updated_at, deleted_at
		FROM dev.stock_alerts 
		WHERE %s = $1 AND deleted_at IS NULL`, prop)

	row := s.pool.QueryRow(ctx, query, value)

	var stockAlert model.StockAlert
	err := row.Scan(
		&stockAlert.ID,
		&stockAlert.InventoryID,
		&stockAlert.AlertType,
		&stockAlert.Status,
		&stockAlert.TriggeredAt,
		&stockAlert.ResolvedAt,
		&stockAlert.CreatedAt,
		&stockAlert.UpdatedAt,
		&stockAlert.DeletedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return &stockAlert, nil
}

// CountByProp implements StockAlertPostgreRepo.
func (s *stockAlertPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	query := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM dev.stock_alerts 
		WHERE %s = $1 AND deleted_at IS NULL`, prop)

	var count int
	err := s.pool.QueryRow(ctx, query, value).Scan(&count)
	return count, err
}
