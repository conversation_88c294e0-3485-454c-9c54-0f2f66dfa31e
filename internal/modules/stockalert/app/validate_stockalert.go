package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
)

// ValidateAlertType implements model.StockAlertUsecase.
func (s *stockAlertUsecase) ValidateAlertType(ctx context.Context, alertType string) error {
	validTypes := []string{
		model.AlertTypeLowStock,
		model.AlertTypeExpired,
		model.AlertTypeOverstock,
	}

	for _, validType := range validTypes {
		if alertType == validType {
			return nil
		}
	}

	return model.StockAlertInvalidTypef("Invalid alert type", nil, nil)
}

// ValidateStatus implements model.StockAlertUsecase.
func (s *stockAlertUsecase) ValidateStatus(ctx context.Context, status string) error {
	validStatuses := []string{
		model.StatusActive,
		model.StatusResolved,
		model.StatusDismissed,
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return nil
		}
	}

	return model.StockAlertInvalidStatusf("Invalid alert status", nil, nil)
}
