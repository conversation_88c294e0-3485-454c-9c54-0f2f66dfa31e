package app

import (
	"context"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
)

// Update implements model.StockAlertUsecase.
func (s *stockAlertUsecase) Update(ctx context.Context, stockAlert model.StockAlertUpdate) error {
	// Get the current stock alert
	currentAlert, err := s.repo.GetByProp(ctx, "id", stockAlert.ID)
	if err != nil {
		return err
	}

	if currentAlert == nil {
		return model.StockAlertNotFoundf("Stock alert not found", nil, nil)
	}

	// Validate alert type if changed
	if currentAlert.AlertType != stockAlert.AlertType {
		if err := s.ValidateAlertType(ctx, stockAlert.AlertType); err != nil {
			return err
		}
	}

	// Validate status if changed
	if currentAlert.Status != stockAlert.Status {
		if err := s.ValidateStatus(ctx, stockAlert.Status); err != nil {
			return err
		}
	}

	// Update the stock alert
	now := time.Now()
	updatedAlert := model.StockAlert{
		ID:          stockAlert.ID,
		InventoryID: stockAlert.InventoryID,
		AlertType:   stockAlert.AlertType,
		Status:      stockAlert.Status,
		TriggeredAt: stockAlert.TriggeredAt,
		ResolvedAt:  stockAlert.ResolvedAt,
		CreatedAt:   currentAlert.CreatedAt,
		UpdatedAt:   &now,
	}

	return s.repo.Update(ctx, updatedAlert)
}
