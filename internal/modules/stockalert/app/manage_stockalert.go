package app

import (
	"context"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// ResolveAlert implements model.StockAlertUsecase.
func (s *stockAlertUsecase) ResolveAlert(ctx context.Context, id string) error {
	// Get the current stock alert
	currentAlert, err := s.repo.GetByProp(ctx, "id", id)
	if err != nil {
		return utils.InternalErrorf("Failed to get stock alert", err, nil)
	}

	if currentAlert == nil {
		return model.StockAlertNotFoundf("Stock alert not found", nil, nil)
	}

	// Check if alert is already resolved or dismissed
	if currentAlert.Status == model.StatusResolved {
		return model.StockAlertAlreadyResolvedf("Stock alert is already resolved", nil, nil)
	}

	if currentAlert.Status == model.StatusDismissed {
		return model.StockAlertAlreadyResolvedf("Stock alert is already dismissed", nil, nil)
	}

	// Update the alert status to resolved
	now := time.Now()
	updatedAlert := *currentAlert
	updatedAlert.Status = model.StatusResolved
	updatedAlert.ResolvedAt = &now
	updatedAlert.UpdatedAt = &now

	return s.repo.Update(ctx, updatedAlert)
}

// DismissAlert implements model.StockAlertUsecase.
func (s *stockAlertUsecase) DismissAlert(ctx context.Context, id string) error {
	// Get the current stock alert
	currentAlert, err := s.repo.GetByProp(ctx, "id", id)
	if err != nil {
		return utils.InternalErrorf("Failed to get stock alert", err, nil)
	}

	if currentAlert == nil {
		return model.StockAlertNotFoundf("Stock alert not found", nil, nil)
	}

	// Check if alert is already resolved or dismissed
	if currentAlert.Status == model.StatusResolved {
		return model.StockAlertAlreadyResolvedf("Stock alert is already resolved", nil, nil)
	}

	if currentAlert.Status == model.StatusDismissed {
		return model.StockAlertAlreadyResolvedf("Stock alert is already dismissed", nil, nil)
	}

	// Update the alert status to dismissed
	now := time.Now()
	updatedAlert := *currentAlert
	updatedAlert.Status = model.StatusDismissed
	updatedAlert.UpdatedAt = &now

	return s.repo.Update(ctx, updatedAlert)
}
