package app

import (
	"context"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Create implements model.StockAlertUsecase.
func (s *stockAlertUsecase) Create(ctx context.Context, stockAlert model.StockAlertCreate) (string, error) {
	// Validate alert type
	if err := s.ValidateAlertType(ctx, stockAlert.AlertType); err != nil {
		return "", err
	}

	// Validate status
	if err := s.ValidateStatus(ctx, stockAlert.Status); err != nil {
		return "", err
	}

	// Check if there's already an active alert for this inventory and alert type
	activeAlerts, err := s.repo.GetActiveAlertsByInventoryID(ctx, stockAlert.InventoryID)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check for existing active alerts", err, nil)
	}

	for _, alert := range activeAlerts {
		if alert.AlertType == stockAlert.AlertType {
			return "", model.StockAlertConflictf("An active alert of this type already exists for this inventory item", nil, nil)
		}
	}

	// Create the stock alert
	now := time.Now()
	newStockAlert := model.StockAlert{
		ID:          utils.UniqueId(),
		InventoryID: stockAlert.InventoryID,
		AlertType:   stockAlert.AlertType,
		Status:      stockAlert.Status,
		TriggeredAt: stockAlert.TriggeredAt,
		CreatedAt:   &now,
		UpdatedAt:   &now,
	}

	if err := s.repo.Create(ctx, newStockAlert); err != nil {
		return "", utils.InternalErrorf("Failed to create stock alert", err, nil)
	}

	return newStockAlert.ID, nil
}
