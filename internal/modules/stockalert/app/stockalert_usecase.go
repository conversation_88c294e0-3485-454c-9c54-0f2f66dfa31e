package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
)

type stockAlertUsecase struct {
	repo model.StockAlertRepository
}

// Delete implements model.StockAlertUsecase.
func (s *stockAlertUsecase) Delete(ctx context.Context, id string) error {
	return s.repo.Delete(ctx, id)
}

// GetAll implements model.StockAlertUsecase.
func (s *stockAlertUsecase) GetAll(ctx context.Context) ([]model.StockAlertWithDetails, error) {
	return s.repo.GetAll(ctx)
}

// GetAllWithDetails implements model.StockAlertUsecase.
func (s *stockAlertUsecase) GetAllWithDetails(ctx context.Context) ([]model.StockAlertWithDetails, error) {
	return s.repo.GetAllWithDetails(ctx)
}

// GetByProp implements model.StockAlertUsecase.
func (s *stockAlertUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.StockAlert, error) {
	return s.repo.GetByProp(ctx, prop, value)
}

// GetByPropWithDetails implements model.StockAlertUsecase.
func (s *stockAlertUsecase) GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockAlertWithDetails, error) {
	return s.repo.GetByPropWithDetails(ctx, prop, value)
}

// GetByInventoryID implements model.StockAlertUsecase.
func (s *stockAlertUsecase) GetByInventoryID(ctx context.Context, inventoryID string) ([]model.StockAlertWithDetails, error) {
	return s.repo.GetByInventoryID(ctx, inventoryID)
}

// GetByStatus implements model.StockAlertUsecase.
func (s *stockAlertUsecase) GetByStatus(ctx context.Context, status string) ([]model.StockAlertWithDetails, error) {
	return s.repo.GetByStatus(ctx, status)
}

// GetByAlertType implements model.StockAlertUsecase.
func (s *stockAlertUsecase) GetByAlertType(ctx context.Context, alertType string) ([]model.StockAlertWithDetails, error) {
	return s.repo.GetByAlertType(ctx, alertType)
}

// GetActiveAlerts implements model.StockAlertUsecase.
func (s *stockAlertUsecase) GetActiveAlerts(ctx context.Context) ([]model.StockAlertWithDetails, error) {
	return s.repo.GetActiveAlerts(ctx)
}

// GetActiveAlertsByInventoryID implements model.StockAlertUsecase.
func (s *stockAlertUsecase) GetActiveAlertsByInventoryID(ctx context.Context, inventoryID string) ([]model.StockAlertWithDetails, error) {
	return s.repo.GetActiveAlertsByInventoryID(ctx, inventoryID)
}

func NewStockAlertUsecase(repo model.StockAlertRepository) model.StockAlertUsecase {
	return &stockAlertUsecase{
		repo: repo,
	}
}
