# StockAlert Module

The StockAlert module provides functionality for managing stock alerts in the inventory system. It allows creating, updating, and managing alerts for different stock conditions.

## Features

- **Alert Types**: Support for different types of stock alerts:
  - `LOW_STOCK`: When inventory levels fall below the reorder point
  - `EXPIRED`: When inventory items have expired
  - `OVERSTOCK`: When inventory levels exceed maximum stock levels

- **Alert Status**: Track the lifecycle of alerts:
  - `ACTIVE`: <PERSON><PERSON> is currently active and needs attention
  - `RESOLVED`: <PERSON><PERSON> has been resolved (issue fixed)
  - `DISMISSED`: <PERSON><PERSON> has been dismissed (acknowledged but not necessarily fixed)

- **Complete Integration**: Full integration with inventory, product, brand, and warehouse data

## Structure

The module follows the standard project architecture:

```
internal/modules/stockalert/
├── model/                  # Domain models and interfaces
│   ├── stockalert.go      # Core StockAlert structs and constants
│   ├── errors.go          # Error definitions
│   ├── repository.go      # Repository interface
│   └── usecase.go         # Use case interface
├── app/                   # Business logic implementation
│   ├── stockalert_usecase.go    # Main use case implementation
│   ├── create_stockalert.go     # Create alert logic
│   ├── update_stockalert.go     # Update alert logic
│   ├── validate_stockalert.go   # Validation logic
│   └── manage_stockalert.go     # Resolve/dismiss logic
├── repo/                  # Repository implementations
│   ├── pg/               # PostgreSQL implementation
│   └── repo/             # Repository wrapper
└── api/                  # API layer
    └── rest/             # REST API handlers
```

## Models

### StockAlert
```go
type StockAlert struct {
    ID          string
    InventoryID string
    AlertType   string // LOW_STOCK, EXPIRED, OVERSTOCK
    Status      string // ACTIVE, RESOLVED, DISMISSED
    TriggeredAt *time.Time
    ResolvedAt  *time.Time
    CreatedAt   *time.Time
    UpdatedAt   *time.Time
    DeletedAt   *time.Time
}
```

### StockAlertWithDetails
Includes complete inventory information with related product, brand, and warehouse data.

## API Endpoints

- `POST /stock-alerts` - Create a new stock alert
- `PUT /stock-alerts` - Update an existing stock alert
- `GET /stock-alerts/{id}` - Get alert by ID
- `GET /stock-alerts` - Get all alerts
- `GET /stock-alerts/inventory/{inventoryId}` - Get alerts by inventory ID
- `GET /stock-alerts/status/{status}` - Get alerts by status
- `GET /stock-alerts/type/{alertType}` - Get alerts by type
- `GET /stock-alerts/active` - Get all active alerts
- `GET /stock-alerts/active/inventory/{inventoryId}` - Get active alerts by inventory
- `PUT /stock-alerts/{id}/resolve` - Resolve an alert
- `PUT /stock-alerts/{id}/dismiss` - Dismiss an alert
- `DELETE /stock-alerts/{id}` - Delete an alert

## Database Schema

The module creates a `stock_alerts` table with proper indexes for performance:

```sql
CREATE TABLE dev.stock_alerts (
    id VARCHAR(255) PRIMARY KEY,
    inventory_id VARCHAR(255) NOT NULL,
    alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('LOW_STOCK', 'EXPIRED', 'OVERSTOCK')),
    status VARCHAR(50) NOT NULL CHECK (status IN ('ACTIVE', 'RESOLVED', 'DISMISSED')),
    triggered_at TIMESTAMPTZ,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (inventory_id) REFERENCES dev.inventory(id)
);
```

## Usage Example

```go
// Create a new stock alert
alertCreate := model.StockAlertCreate{
    InventoryID: "inventory-123",
    AlertType:   model.AlertTypeLowStock,
    Status:      model.StatusActive,
    TriggeredAt: &time.Now(),
}

id, err := usecase.Create(ctx, alertCreate)
if err != nil {
    // Handle error
}

// Resolve an alert
err = usecase.ResolveAlert(ctx, id)
if err != nil {
    // Handle error
}
```

## Error Handling

The module provides specific error types for different scenarios:
- `StockAlertConflictCode`: When trying to create duplicate active alerts
- `StockAlertNotFoundCode`: When alert doesn't exist
- `StockAlertInvalidStatusCode`: When using invalid status
- `StockAlertInvalidTypeCode`: When using invalid alert type
- `StockAlertAlreadyResolvedCode`: When trying to modify resolved/dismissed alerts
