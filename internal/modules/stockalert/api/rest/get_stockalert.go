package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// GetById implements StockAlertHandler.
func (s *stockAlertHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	stockAlert, err := s.useCase.GetByPropWithDetails(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock alert")
		return
	}

	if stockAlert == nil {
		rest.ErrorResponse(w, r, utils.NotFoundf("Stock alert not found", nil, nil), http.StatusNotFound)
		return
	}

	rest.SuccessDResponse(w, r, stockAlert, http.StatusOK)
}

// GetAll implements StockAlertHandler.
func (s *stockAlertHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	stockAlerts, err := s.useCase.GetAllWithDetails(ctx)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock alerts")
		return
	}

	rest.SuccessDResponse(w, r, stockAlerts, http.StatusOK)
}

// GetByInventoryID implements StockAlertHandler.
func (s *stockAlertHandler) GetByInventoryID(w http.ResponseWriter, r *http.Request) {
	inventoryID := r.PathValue("inventoryId")
	ctx := r.Context()

	if inventoryID == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Inventory ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	stockAlerts, err := s.useCase.GetByInventoryID(ctx, inventoryID)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock alerts by inventory ID")
		return
	}

	rest.SuccessDResponse(w, r, stockAlerts, http.StatusOK)
}

// GetByStatus implements StockAlertHandler.
func (s *stockAlertHandler) GetByStatus(w http.ResponseWriter, r *http.Request) {
	status := r.PathValue("status")
	ctx := r.Context()

	if status == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Status parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	stockAlerts, err := s.useCase.GetByStatus(ctx, status)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock alerts by status")
		return
	}

	rest.SuccessDResponse(w, r, stockAlerts, http.StatusOK)
}

// GetByAlertType implements StockAlertHandler.
func (s *stockAlertHandler) GetByAlertType(w http.ResponseWriter, r *http.Request) {
	alertType := r.PathValue("alertType")
	ctx := r.Context()

	if alertType == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Alert type parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	stockAlerts, err := s.useCase.GetByAlertType(ctx, alertType)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock alerts by alert type")
		return
	}

	rest.SuccessDResponse(w, r, stockAlerts, http.StatusOK)
}

// GetActiveAlerts implements StockAlertHandler.
func (s *stockAlertHandler) GetActiveAlerts(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	stockAlerts, err := s.useCase.GetActiveAlerts(ctx)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get active stock alerts")
		return
	}

	rest.SuccessDResponse(w, r, stockAlerts, http.StatusOK)
}

// GetActiveAlertsByInventoryID implements StockAlertHandler.
func (s *stockAlertHandler) GetActiveAlertsByInventoryID(w http.ResponseWriter, r *http.Request) {
	inventoryID := r.PathValue("inventoryId")
	ctx := r.Context()

	if inventoryID == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Inventory ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	stockAlerts, err := s.useCase.GetActiveAlertsByInventoryID(ctx, inventoryID)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get active stock alerts by inventory ID")
		return
	}

	rest.SuccessDResponse(w, r, stockAlerts, http.StatusOK)
}
