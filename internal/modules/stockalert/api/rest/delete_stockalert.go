package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Delete implements StockAlertHandler.
func (s *stockAlertHandler) Delete(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := s.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to delete stock alert")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
