package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type stockAlertCreate struct {
	InventoryID string     `json:"inventory_id" validate:"required"`
	AlertType   string     `json:"alert_type" validate:"required,oneof=LOW_STOCK EXPIRED OVERSTOCK"`
	Status      string     `json:"status" validate:"required,oneof=ACTIVE RESOLVED DISMISSED"`
	TriggeredAt *time.Time `json:"triggered_at"`
}

func stockAlertCreateToModel(req stockAlertCreate) model.StockAlertCreate {
	return model.StockAlertCreate{
		InventoryID: req.InventoryID,
		AlertType:   req.AlertType,
		Status:      req.Status,
		TriggeredAt: req.TriggeredAt,
	}
}

// Create implements StockAlertHandler.
func (s *stockAlertHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[stockAlertCreate](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	id, err := s.useCase.Create(ctx, stockAlertCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to create stock alert")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
