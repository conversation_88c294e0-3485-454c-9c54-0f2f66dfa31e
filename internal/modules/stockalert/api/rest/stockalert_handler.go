package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type StockAlertHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	GetByInventoryID(w http.ResponseWriter, r *http.Request)
	GetByStatus(w http.ResponseWriter, r *http.Request)
	GetByAlertType(w http.ResponseWriter, r *http.Request)
	GetActiveAlerts(w http.ResponseWriter, r *http.Request)
	GetActiveAlertsByInventoryID(w http.ResponseWriter, r *http.Request)
	ResolveAlert(w http.ResponseWriter, r *http.Request)
	DismissAlert(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
}

type stockAlertHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.StockAlertUsecase
}

func NewStockAlertHandler(log *logrus.Logger, validator *validator.Validate, useCase model.StockAlertUsecase) StockAlertHandler {
	return &stockAlertHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
