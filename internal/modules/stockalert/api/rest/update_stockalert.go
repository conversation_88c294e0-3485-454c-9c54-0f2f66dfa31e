package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type stockAlertUpdate struct {
	ID          string     `json:"id" validate:"required"`
	InventoryID string     `json:"inventory_id" validate:"required"`
	AlertType   string     `json:"alert_type" validate:"required,oneof=LOW_STOCK EXPIRED OVERSTOCK"`
	Status      string     `json:"status" validate:"required,oneof=ACTIVE RESOLVED DISMISSED"`
	TriggeredAt *time.Time `json:"triggered_at"`
	ResolvedAt  *time.Time `json:"resolved_at"`
}

func stockAlertUpdateToModel(req stockAlertUpdate) model.StockAlertUpdate {
	return model.StockAlertUpdate{
		ID:          req.ID,
		InventoryID: req.InventoryID,
		AlertType:   req.AlertType,
		Status:      req.Status,
		TriggeredAt: req.TriggeredAt,
		ResolvedAt:  req.ResolvedAt,
	}
}

// Update implements StockAlertHandler.
func (s *stockAlertHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[stockAlertUpdate](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	err = s.useCase.Update(ctx, stockAlertUpdateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to update stock alert")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
