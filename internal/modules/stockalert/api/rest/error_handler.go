package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockalert/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.StockAlertConflictCode:        http.StatusConflict,
	model.StockAlertNotFoundCode:        http.StatusNotFound,
	model.StockAlertValidationErrorCode: http.StatusBadRequest,
	model.StockAlertInvalidStatusCode:   http.StatusBadRequest,
	model.StockAlertInvalidTypeCode:     http.StatusBadRequest,
	model.StockAlertAlreadyResolvedCode: http.StatusBadRequest,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
