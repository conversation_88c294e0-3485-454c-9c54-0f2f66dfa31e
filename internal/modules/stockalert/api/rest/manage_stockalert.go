package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// ResolveAlert implements StockAlertHandler.
func (s *stockAlertHandler) ResolveAlert(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := s.useCase.ResolveAlert(ctx, id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to resolve stock alert")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

// DismissAlert implements StockAlertHandler.
func (s *stockAlertHandler) DismissAlert(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := s.useCase.DismissAlert(ctx, id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to dismiss stock alert")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
