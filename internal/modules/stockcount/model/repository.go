package model

import (
	"context"
	"time"
)

type StockCountRepository interface {
	// StockCount operations
	Create(ctx context.Context, stockCount StockCount) error
	Update(ctx context.Context, stockCount StockCount) error
	GetByProp(ctx context.Context, prop string, value string) (*StockCount, error)
	GetByPropWithDetails(ctx context.Context, prop string, value string) (*StockCountWithDetails, error)
	GetAll(ctx context.Context) ([]StockCountWithDetails, error)
	GetByWarehouseID(ctx context.Context, warehouseID string) ([]StockCountWithDetails, error)
	GetByStatus(ctx context.Context, status string) ([]StockCountWithDetails, error)
	GetByCountType(ctx context.Context, countType string) ([]StockCountWithDetails, error)
	Delete(ctx context.Context, id string) error
	CountByProp(ctx context.Context, prop string, value string) (int, error)

	// StockCountLine operations
	CreateLine(ctx context.Context, line StockCountLine) error
	UpdateLine(ctx context.Context, line StockCountLine) error
	GetLineByProp(ctx context.Context, prop string, value string) (*StockCountLine, error)
	GetLineByPropWithDetails(ctx context.Context, prop string, value string) (*StockCountLineWithDetails, error)
	GetLinesByStockCountID(ctx context.Context, stockCountID string) ([]StockCountLineWithDetails, error)
	GetLinesByInventoryID(ctx context.Context, inventoryID string) ([]StockCountLineWithDetails, error)
	DeleteLine(ctx context.Context, id string) error
	CountLinesByProp(ctx context.Context, prop string, value string) (int, error)
	CountLinesByStockCountInventory(ctx context.Context, stockCountID, inventoryID string) (int, error)

	// Batch operations
	CreateLinesForStockCount(ctx context.Context, stockCountID string, inventoryIDs []string) error
	UpdateStockCountStatus(ctx context.Context, stockCountID string, status string, completedDate *time.Time) error
	CalculateVariances(ctx context.Context, stockCountID string) error
}
