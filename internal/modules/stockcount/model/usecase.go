package model

import (
	"context"
)

type StockCountUsecase interface {
	// StockCount operations
	Create(ctx context.Context, stockCount StockCountCreate) (string, error)
	Update(ctx context.Context, stockCount StockCountUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*StockCount, error)
	GetByPropWithDetails(ctx context.Context, prop string, value string) (*StockCountWithDetails, error)
	GetAll(ctx context.Context) ([]StockCountWithDetails, error)
	GetByWarehouseID(ctx context.Context, warehouseID string) ([]StockCountWithDetails, error)
	GetByStatus(ctx context.Context, status string) ([]StockCountWithDetails, error)
	GetByCountType(ctx context.Context, countType string) ([]StockCountWithDetails, error)
	Delete(ctx context.Context, id string) error

	// StockCountLine operations
	CreateLine(ctx context.Context, line StockCountLineCreate) (string, error)
	UpdateLine(ctx context.Context, line StockCountLineUpdate) error
	GetLineByProp(ctx context.Context, prop string, value string) (*StockCountLine, error)
	GetLineByPropWithDetails(ctx context.Context, prop string, value string) (*StockCountLineWithDetails, error)
	GetLinesByStockCountID(ctx context.Context, stockCountID string) ([]StockCountLineWithDetails, error)
	GetLinesByInventoryID(ctx context.Context, inventoryID string) ([]StockCountLineWithDetails, error)
	DeleteLine(ctx context.Context, id string) error

	// Business operations
	StartStockCount(ctx context.Context, stockCountID string) error
	CompleteStockCount(ctx context.Context, stockCountID string) error
	GenerateLinesForWarehouse(ctx context.Context, stockCountID string) error
	UpdateLineCount(ctx context.Context, lineID string, countedQuantity float64, countedByUserID string) error
	CalculateVariances(ctx context.Context, stockCountID string) error

	// Validation
	ValidateStockCountStatus(ctx context.Context, stockCountID string, expectedStatus string) error
	ValidateLineExists(ctx context.Context, stockCountID, inventoryID string) error
}
