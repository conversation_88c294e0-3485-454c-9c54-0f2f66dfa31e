package model

import (
	"time"

	inventoryModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/inventory/model"
	userModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/model"
	warehouseModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
)

type StockCount struct {
	ID            string
	WarehouseID   string
	CountType     string // FULL, CYCLE, SPOT
	Status        string // PLANNED, IN_PROGRESS, COMPLETED
	ScheduledDate *time.Time
	CompletedDate *time.Time
	CreatedAt     *time.Time
	UpdatedAt     *time.Time
	DeletedAt     *time.Time
}

type StockCountLine struct {
	ID               string
	StockCountID     string
	InventoryID      string
	SystemQuantity   float64
	CountedQuantity  *float64
	Variance         *float64
	CountedByUserID  *string
	CreatedAt        *time.Time
	UpdatedAt        *time.Time
	DeletedAt        *time.Time
}

type StockCountCreate struct {
	WarehouseID   string
	CountType     string
	Status        string
	ScheduledDate *time.Time
}

type StockCountUpdate struct {
	ID            string
	WarehouseID   string
	CountType     string
	Status        string
	ScheduledDate *time.Time
	CompletedDate *time.Time
}

type StockCountLineCreate struct {
	StockCountID     string
	InventoryID      string
	SystemQuantity   float64
	CountedQuantity  *float64
	CountedByUserID  *string
}

type StockCountLineUpdate struct {
	ID              string
	CountedQuantity *float64
	CountedByUserID *string
}

// StockCountWithDetails includes complete information about warehouse and lines
type StockCountWithDetails struct {
	ID            string
	WarehouseID   string
	CountType     string
	Status        string
	ScheduledDate *time.Time
	CompletedDate *time.Time
	CreatedAt     *time.Time
	UpdatedAt     *time.Time
	DeletedAt     *time.Time
	// Related entities
	Warehouse warehouseModel.Warehouse
	Lines     []StockCountLineWithDetails
}

// StockCountLineWithDetails includes complete information about inventory and user
type StockCountLineWithDetails struct {
	ID               string
	StockCountID     string
	InventoryID      string
	SystemQuantity   float64
	CountedQuantity  *float64
	Variance         *float64
	CountedByUserID  *string
	CreatedAt        *time.Time
	UpdatedAt        *time.Time
	DeletedAt        *time.Time
	// Related entities
	Inventory   inventoryModel.InventoryWithDetails
	CountedByUser *userModel.User
}

// Constants for CountType
const (
	CountTypeFull  = "FULL"
	CountTypeCycle = "CYCLE"
	CountTypeSpot  = "SPOT"
)

// Constants for Status
const (
	StatusPlanned    = "PLANNED"
	StatusInProgress = "IN_PROGRESS"
	StatusCompleted  = "COMPLETED"
)
