package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	StockCountConflictCode utils.ErrCode = utils.StockCountCode + iota
	StockCountNotFoundCode
	StockCountInvalidStatusCode
	StockCountInvalidCountTypeCode
	StockCountAlreadyStartedCode
	StockCountAlreadyCompletedCode
	StockCountNotStartedCode
	StockCountLineConflictCode
	StockCountLineNotFoundCode
	StockCountLineInvalidQuantityCode
	StockCountLineAlreadyCountedCode
	StockCountInvalidWarehouseCode
	StockCountInvalidInventoryCode
	StockCountInvalidUserCode
)

func StockCountConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountConflictCode, message, err, details)
}

func StockCountNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountNotFoundCode, message, err, details)
}

func StockCountInvalidStatusf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountInvalidStatusCode, message, err, details)
}

func StockCountInvalidCountTypef(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountInvalidCountTypeCode, message, err, details)
}

func StockCountAlreadyStartedf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountAlreadyStartedCode, message, err, details)
}

func StockCountAlreadyCompletedf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountAlreadyCompletedCode, message, err, details)
}

func StockCountNotStartedf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountNotStartedCode, message, err, details)
}

func StockCountLineConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountLineConflictCode, message, err, details)
}

func StockCountLineNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountLineNotFoundCode, message, err, details)
}

func StockCountLineInvalidQuantityf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountLineInvalidQuantityCode, message, err, details)
}

func StockCountLineAlreadyCountedf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountLineAlreadyCountedCode, message, err, details)
}

func StockCountInvalidWarehouseF(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountInvalidWarehouseCode, message, err, details)
}

func StockCountInvalidInventoryf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountInvalidInventoryCode, message, err, details)
}

func StockCountInvalidUserf(message string, err error, details any) utils.AppErr {
	return utils.NewError(StockCountInvalidUserCode, message, err, details)
}
