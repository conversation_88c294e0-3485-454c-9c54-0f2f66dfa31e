package app

import (
	"context"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.StockCountUsecase.
func (s *stockCountUsecase) Update(ctx context.Context, stockCount model.StockCountUpdate) error {
	// Validate that stock count exists
	existing, err := s.repo.GetByProp(ctx, "id", stockCount.ID)
	if err != nil {
		return model.StockCountNotFoundf("Stock count not found", err, nil)
	}

	// Validate count type
	if stockCount.CountType != model.CountTypeFull && 
	   stockCount.CountType != model.CountTypeCycle && 
	   stockCount.CountType != model.CountTypeSpot {
		return model.StockCountInvalidCountTypef("Invalid count type", nil, map[string]interface{}{
			"count_type": stockCount.CountType,
			"valid_types": []string{model.CountTypeFull, model.CountTypeCycle, model.CountTypeSpot},
		})
	}

	// Validate status
	if stockCount.Status != model.StatusPlanned && 
	   stockCount.Status != model.StatusInProgress && 
	   stockCount.Status != model.StatusCompleted {
		return model.StockCountInvalidStatusf("Invalid status", nil, map[string]interface{}{
			"status": stockCount.Status,
			"valid_statuses": []string{model.StatusPlanned, model.StatusInProgress, model.StatusCompleted},
		})
	}

	// Validate status transitions
	if existing.Status == model.StatusCompleted && stockCount.Status != model.StatusCompleted {
		return model.StockCountAlreadyCompletedf("Cannot change status of completed stock count", nil, nil)
	}

	// Validate scheduled date
	if stockCount.ScheduledDate != nil && stockCount.ScheduledDate.Before(time.Now()) {
		return model.StockCountInvalidStatusf("Scheduled date cannot be in the past", nil, nil)
	}

	// Create updated entity
	updatedEntity := model.StockCount{
		ID:            stockCount.ID,
		WarehouseID:   stockCount.WarehouseID,
		CountType:     stockCount.CountType,
		Status:        stockCount.Status,
		ScheduledDate: stockCount.ScheduledDate,
		CompletedDate: stockCount.CompletedDate,
	}

	// Save to repository
	err = s.repo.Update(ctx, updatedEntity)
	if err != nil {
		return utils.InternalErrorf("Failed to update stock count", err, nil)
	}

	return nil
}

// UpdateLine implements model.StockCountUsecase.
func (s *stockCountUsecase) UpdateLine(ctx context.Context, line model.StockCountLineUpdate) error {
	// Validate that line exists
	existing, err := s.repo.GetLineByProp(ctx, "id", line.ID)
	if err != nil {
		return model.StockCountLineNotFoundf("Stock count line not found", err, nil)
	}

	// Validate counted quantity if provided
	if line.CountedQuantity != nil && *line.CountedQuantity < 0 {
		return model.StockCountLineInvalidQuantityf("Counted quantity cannot be negative", nil, nil)
	}

	// Calculate variance if counted quantity is provided
	var variance *float64
	if line.CountedQuantity != nil {
		v := *line.CountedQuantity - existing.SystemQuantity
		variance = &v
	}

	// Create updated entity
	updatedEntity := model.StockCountLine{
		ID:               line.ID,
		StockCountID:     existing.StockCountID,
		InventoryID:      existing.InventoryID,
		SystemQuantity:   existing.SystemQuantity,
		CountedQuantity:  line.CountedQuantity,
		Variance:         variance,
		CountedByUserID:  line.CountedByUserID,
	}

	// Save to repository
	err = s.repo.UpdateLine(ctx, updatedEntity)
	if err != nil {
		return utils.InternalErrorf("Failed to update stock count line", err, nil)
	}

	return nil
}

// StartStockCount implements model.StockCountUsecase.
func (s *stockCountUsecase) StartStockCount(ctx context.Context, stockCountID string) error {
	// Validate current status
	err := s.ValidateStockCountStatus(ctx, stockCountID, model.StatusPlanned)
	if err != nil {
		return err
	}

	// Update status to in progress
	err = s.repo.UpdateStockCountStatus(ctx, stockCountID, model.StatusInProgress, nil)
	if err != nil {
		return utils.InternalErrorf("Failed to start stock count", err, nil)
	}

	return nil
}

// CompleteStockCount implements model.StockCountUsecase.
func (s *stockCountUsecase) CompleteStockCount(ctx context.Context, stockCountID string) error {
	// Validate current status
	err := s.ValidateStockCountStatus(ctx, stockCountID, model.StatusInProgress)
	if err != nil {
		return err
	}

	// Calculate variances for all lines
	err = s.repo.CalculateVariances(ctx, stockCountID)
	if err != nil {
		return utils.InternalErrorf("Failed to calculate variances", err, nil)
	}

	// Update status to completed with completion date
	now := time.Now()
	err = s.repo.UpdateStockCountStatus(ctx, stockCountID, model.StatusCompleted, &now)
	if err != nil {
		return utils.InternalErrorf("Failed to complete stock count", err, nil)
	}

	return nil
}

// GenerateLinesForWarehouse implements model.StockCountUsecase.
func (s *stockCountUsecase) GenerateLinesForWarehouse(ctx context.Context, stockCountID string) error {
	// Validate that stock count exists and is in planned status
	stockCount, err := s.repo.GetByProp(ctx, "id", stockCountID)
	if err != nil {
		return model.StockCountNotFoundf("Stock count not found", err, nil)
	}

	if stockCount.Status != model.StatusPlanned {
		return model.StockCountInvalidStatusf("Can only generate lines for planned stock counts", nil, map[string]interface{}{
			"current_status": stockCount.Status,
		})
	}

	// TODO: Get all inventory items for the warehouse
	// This would require integration with inventory service
	// For now, we'll create a placeholder implementation
	var inventoryIDs []string // This should be populated from inventory service

	// Create lines for all inventory items
	err = s.repo.CreateLinesForStockCount(ctx, stockCountID, inventoryIDs)
	if err != nil {
		return utils.InternalErrorf("Failed to generate stock count lines", err, nil)
	}

	return nil
}

// UpdateLineCount implements model.StockCountUsecase.
func (s *stockCountUsecase) UpdateLineCount(ctx context.Context, lineID string, countedQuantity float64, countedByUserID string) error {
	// Validate counted quantity
	if countedQuantity < 0 {
		return model.StockCountLineInvalidQuantityf("Counted quantity cannot be negative", nil, nil)
	}

	// Update the line
	updateData := model.StockCountLineUpdate{
		ID:              lineID,
		CountedQuantity: &countedQuantity,
		CountedByUserID: &countedByUserID,
	}

	return s.UpdateLine(ctx, updateData)
}

// CalculateVariances implements model.StockCountUsecase.
func (s *stockCountUsecase) CalculateVariances(ctx context.Context, stockCountID string) error {
	// Validate that stock count exists
	_, err := s.repo.GetByProp(ctx, "id", stockCountID)
	if err != nil {
		return model.StockCountNotFoundf("Stock count not found", err, nil)
	}

	// Calculate variances
	err = s.repo.CalculateVariances(ctx, stockCountID)
	if err != nil {
		return utils.InternalErrorf("Failed to calculate variances", err, nil)
	}

	return nil
}
