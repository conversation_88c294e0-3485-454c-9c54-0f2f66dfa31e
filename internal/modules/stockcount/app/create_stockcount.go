package app

import (
	"context"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/oklog/ulid/v2"
)

// Create implements model.StockCountUsecase.
func (s *stockCountUsecase) Create(ctx context.Context, stockCount model.StockCountCreate) (string, error) {
	// Validate count type
	if stockCount.CountType != model.CountTypeFull && 
	   stockCount.CountType != model.CountTypeCycle && 
	   stockCount.CountType != model.CountTypeSpot {
		return "", model.StockCountInvalidCountTypef("Invalid count type", nil, map[string]interface{}{
			"count_type": stockCount.CountType,
			"valid_types": []string{model.CountTypeFull, model.CountTypeCycle, model.CountTypeSpot},
		})
	}

	// Validate status
	if stockCount.Status != model.StatusPlanned && 
	   stockCount.Status != model.StatusInProgress && 
	   stockCount.Status != model.StatusCompleted {
		return "", model.StockCountInvalidStatusf("Invalid status", nil, map[string]interface{}{
			"status": stockCount.Status,
			"valid_statuses": []string{model.StatusPlanned, model.StatusInProgress, model.StatusCompleted},
		})
	}

	// Validate scheduled date
	if stockCount.ScheduledDate != nil && stockCount.ScheduledDate.Before(time.Now()) {
		return "", model.StockCountInvalidStatusf("Scheduled date cannot be in the past", nil, nil)
	}

	// Create stock count entity
	id := ulid.Make().String()
	stockCountEntity := model.StockCount{
		ID:            id,
		WarehouseID:   stockCount.WarehouseID,
		CountType:     stockCount.CountType,
		Status:        stockCount.Status,
		ScheduledDate: stockCount.ScheduledDate,
		CompletedDate: nil, // Will be set when completed
	}

	// Save to repository
	err := s.repo.Create(ctx, stockCountEntity)
	if err != nil {
		return "", utils.InternalErrorf("Failed to create stock count", err, nil)
	}

	return id, nil
}

// CreateLine implements model.StockCountUsecase.
func (s *stockCountUsecase) CreateLine(ctx context.Context, line model.StockCountLineCreate) (string, error) {
	// Validate that stock count exists
	_, err := s.repo.GetByProp(ctx, "id", line.StockCountID)
	if err != nil {
		return "", model.StockCountNotFoundf("Stock count not found", err, nil)
	}

	// Validate that line doesn't already exist for this inventory
	err = s.ValidateLineExists(ctx, line.StockCountID, line.InventoryID)
	if err != nil {
		return "", err
	}

	// Validate system quantity
	if line.SystemQuantity < 0 {
		return "", model.StockCountLineInvalidQuantityf("System quantity cannot be negative", nil, nil)
	}

	// Validate counted quantity if provided
	if line.CountedQuantity != nil && *line.CountedQuantity < 0 {
		return "", model.StockCountLineInvalidQuantityf("Counted quantity cannot be negative", nil, nil)
	}

	// Calculate variance if counted quantity is provided
	var variance *float64
	if line.CountedQuantity != nil {
		v := *line.CountedQuantity - line.SystemQuantity
		variance = &v
	}

	// Create stock count line entity
	id := ulid.Make().String()
	lineEntity := model.StockCountLine{
		ID:               id,
		StockCountID:     line.StockCountID,
		InventoryID:      line.InventoryID,
		SystemQuantity:   line.SystemQuantity,
		CountedQuantity:  line.CountedQuantity,
		Variance:         variance,
		CountedByUserID:  line.CountedByUserID,
	}

	// Save to repository
	err = s.repo.CreateLine(ctx, lineEntity)
	if err != nil {
		return "", utils.InternalErrorf("Failed to create stock count line", err, nil)
	}

	return id, nil
}
