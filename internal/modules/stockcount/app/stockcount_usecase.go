package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/model"
)

type stockCountUsecase struct {
	repo model.StockCountRepository
}

// Delete implements model.StockCountUsecase.
func (s *stockCountUsecase) Delete(ctx context.Context, id string) error {
	return s.repo.Delete(ctx, id)
}

// GetAll implements model.StockCountUsecase.
func (s *stockCountUsecase) GetAll(ctx context.Context) ([]model.StockCountWithDetails, error) {
	return s.repo.GetAll(ctx)
}

// GetByProp implements model.StockCountUsecase.
func (s *stockCountUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.StockCount, error) {
	return s.repo.GetByProp(ctx, prop, value)
}

// GetByPropWithDetails implements model.StockCountUsecase.
func (s *stockCountUsecase) GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockCountWithDetails, error) {
	return s.repo.GetByPropWithDetails(ctx, prop, value)
}

// GetByWarehouseID implements model.StockCountUsecase.
func (s *stockCountUsecase) GetByWarehouseID(ctx context.Context, warehouseID string) ([]model.StockCountWithDetails, error) {
	return s.repo.GetByWarehouseID(ctx, warehouseID)
}

// GetByStatus implements model.StockCountUsecase.
func (s *stockCountUsecase) GetByStatus(ctx context.Context, status string) ([]model.StockCountWithDetails, error) {
	return s.repo.GetByStatus(ctx, status)
}

// GetByCountType implements model.StockCountUsecase.
func (s *stockCountUsecase) GetByCountType(ctx context.Context, countType string) ([]model.StockCountWithDetails, error) {
	return s.repo.GetByCountType(ctx, countType)
}

// DeleteLine implements model.StockCountUsecase.
func (s *stockCountUsecase) DeleteLine(ctx context.Context, id string) error {
	return s.repo.DeleteLine(ctx, id)
}

// GetLineByProp implements model.StockCountUsecase.
func (s *stockCountUsecase) GetLineByProp(ctx context.Context, prop string, value string) (*model.StockCountLine, error) {
	return s.repo.GetLineByProp(ctx, prop, value)
}

// GetLineByPropWithDetails implements model.StockCountUsecase.
func (s *stockCountUsecase) GetLineByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockCountLineWithDetails, error) {
	return s.repo.GetLineByPropWithDetails(ctx, prop, value)
}

// GetLinesByStockCountID implements model.StockCountUsecase.
func (s *stockCountUsecase) GetLinesByStockCountID(ctx context.Context, stockCountID string) ([]model.StockCountLineWithDetails, error) {
	return s.repo.GetLinesByStockCountID(ctx, stockCountID)
}

// GetLinesByInventoryID implements model.StockCountUsecase.
func (s *stockCountUsecase) GetLinesByInventoryID(ctx context.Context, inventoryID string) ([]model.StockCountLineWithDetails, error) {
	return s.repo.GetLinesByInventoryID(ctx, inventoryID)
}

// ValidateStockCountStatus implements model.StockCountUsecase.
func (s *stockCountUsecase) ValidateStockCountStatus(ctx context.Context, stockCountID string, expectedStatus string) error {
	stockCount, err := s.repo.GetByProp(ctx, "id", stockCountID)
	if err != nil {
		return model.StockCountNotFoundf("Stock count not found", err, nil)
	}

	if stockCount.Status != expectedStatus {
		return model.StockCountInvalidStatusf("Stock count status is not "+expectedStatus, nil, map[string]interface{}{
			"current_status":  stockCount.Status,
			"expected_status": expectedStatus,
		})
	}

	return nil
}

// ValidateLineExists implements model.StockCountUsecase.
func (s *stockCountUsecase) ValidateLineExists(ctx context.Context, stockCountID, inventoryID string) error {
	count, err := s.repo.CountLinesByStockCountInventory(ctx, stockCountID, inventoryID)
	if err != nil {
		return err
	}

	if count > 0 {
		return model.StockCountLineConflictf("Stock count line already exists for this inventory", nil, map[string]interface{}{
			"stock_count_id": stockCountID,
			"inventory_id":   inventoryID,
		})
	}

	return nil
}

func NewStockCountUsecase(repo model.StockCountRepository) model.StockCountUsecase {
	return &stockCountUsecase{
		repo: repo,
	}
}
