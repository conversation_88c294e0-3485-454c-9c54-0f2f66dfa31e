package pg

import (
	"context"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type StockCountPostgreRepo interface {
	// StockCount operations
	Create(ctx context.Context, stockCount model.StockCount) error
	Update(ctx context.Context, stockCount model.StockCount) error
	GetByProp(ctx context.Context, prop string, value string) (*model.StockCount, error)
	GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockCountWithDetails, error)
	GetAll(ctx context.Context) ([]model.StockCountWithDetails, error)
	GetByWarehouseID(ctx context.Context, warehouseID string) ([]model.StockCountWithDetails, error)
	GetByStatus(ctx context.Context, status string) ([]model.StockCountWithDetails, error)
	GetByCountType(ctx context.Context, countType string) ([]model.StockCountWithDetails, error)
	Delete(ctx context.Context, id string) error
	CountByProp(ctx context.Context, prop string, value string) (int, error)

	// StockCountLine operations
	CreateLine(ctx context.Context, line model.StockCountLine) error
	UpdateLine(ctx context.Context, line model.StockCountLine) error
	GetLineByProp(ctx context.Context, prop string, value string) (*model.StockCountLine, error)
	GetLineByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockCountLineWithDetails, error)
	GetLinesByStockCountID(ctx context.Context, stockCountID string) ([]model.StockCountLineWithDetails, error)
	GetLinesByInventoryID(ctx context.Context, inventoryID string) ([]model.StockCountLineWithDetails, error)
	DeleteLine(ctx context.Context, id string) error
	CountLinesByProp(ctx context.Context, prop string, value string) (int, error)
	CountLinesByStockCountInventory(ctx context.Context, stockCountID, inventoryID string) (int, error)

	// Batch operations
	CreateLinesForStockCount(ctx context.Context, stockCountID string, inventoryIDs []string) error
	UpdateStockCountStatus(ctx context.Context, stockCountID string, status string, completedDate *time.Time) error
	CalculateVariances(ctx context.Context, stockCountID string) error
}

type stockCountPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewStockCountPostgreRepo(pool *pgxpool.Pool) StockCountPostgreRepo {
	return &stockCountPostgreRepo{
		pool: pool,
	}
}

// Create implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) Create(ctx context.Context, stockCount model.StockCount) error {
	query := `
		INSERT INTO stock_counts (id, warehouse_id, count_type, status, scheduled_date, completed_date, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
	`
	_, err := s.pool.Exec(ctx, query,
		stockCount.ID,
		stockCount.WarehouseID,
		stockCount.CountType,
		stockCount.Status,
		stockCount.ScheduledDate,
		stockCount.CompletedDate,
	)
	return err
}

// Update implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) Update(ctx context.Context, stockCount model.StockCount) error {
	query := `
		UPDATE stock_counts 
		SET warehouse_id = $2, count_type = $3, status = $4, scheduled_date = $5, completed_date = $6, updated_at = NOW()
		WHERE id = $1 AND deleted_at IS NULL
	`
	_, err := s.pool.Exec(ctx, query,
		stockCount.ID,
		stockCount.WarehouseID,
		stockCount.CountType,
		stockCount.Status,
		stockCount.ScheduledDate,
		stockCount.CompletedDate,
	)
	return err
}

// GetByProp implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.StockCount, error) {
	query := `
		SELECT id, warehouse_id, count_type, status, scheduled_date, completed_date, created_at, updated_at, deleted_at
		FROM stock_counts 
		WHERE ` + prop + ` = $1 AND deleted_at IS NULL
	`

	var stockCount model.StockCount
	err := s.pool.QueryRow(ctx, query, value).Scan(
		&stockCount.ID,
		&stockCount.WarehouseID,
		&stockCount.CountType,
		&stockCount.Status,
		&stockCount.ScheduledDate,
		&stockCount.CompletedDate,
		&stockCount.CreatedAt,
		&stockCount.UpdatedAt,
		&stockCount.DeletedAt,
	)
	if err != nil {
		return nil, err
	}

	return &stockCount, nil
}

// GetByPropWithDetails implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockCountWithDetails, error) {
	query := `
		SELECT
			sc.id, sc.warehouse_id, sc.count_type, sc.status, sc.scheduled_date, sc.completed_date,
			sc.created_at, sc.updated_at, sc.deleted_at,
			w.id, w.name, w.code, w.type, w.category, w.description, w.address, w.is_active, w.is_system_warehouse,
			w.created_at, w.updated_at, w.deleted_at
		FROM stock_counts sc
		LEFT JOIN warehouses w ON sc.warehouse_id = w.id
		WHERE sc.` + prop + ` = $1 AND sc.deleted_at IS NULL
	`

	var result model.StockCountWithDetails
	err := s.pool.QueryRow(ctx, query, value).Scan(
		&result.ID,
		&result.WarehouseID,
		&result.CountType,
		&result.Status,
		&result.ScheduledDate,
		&result.CompletedDate,
		&result.CreatedAt,
		&result.UpdatedAt,
		&result.DeletedAt,
		&result.Warehouse.ID,
		&result.Warehouse.Name,
		&result.Warehouse.Code,
		&result.Warehouse.Type,
		&result.Warehouse.Category,
		&result.Warehouse.Description,
		&result.Warehouse.Address,
		&result.Warehouse.IsActive,
		&result.Warehouse.IsSystemWarehouse,
		&result.Warehouse.CreatedAt,
		&result.Warehouse.UpdatedAt,
		&result.Warehouse.DeletedAt,
	)
	if err != nil {
		return nil, err
	}

	// Get lines for this stock count
	lines, err := s.GetLinesByStockCountID(ctx, result.ID)
	if err != nil {
		return nil, err
	}
	result.Lines = lines

	return &result, nil
}

// GetAll implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) GetAll(ctx context.Context) ([]model.StockCountWithDetails, error) {
	query := `
		SELECT
			sc.id, sc.warehouse_id, sc.count_type, sc.status, sc.scheduled_date, sc.completed_date,
			sc.created_at, sc.updated_at, sc.deleted_at,
			w.id, w.name, w.code, w.type, w.category, w.description, w.address, w.is_active, w.is_system_warehouse,
			w.created_at, w.updated_at, w.deleted_at
		FROM stock_counts sc
		LEFT JOIN warehouses w ON sc.warehouse_id = w.id
		WHERE sc.deleted_at IS NULL
		ORDER BY sc.created_at DESC
	`

	rows, err := s.pool.Query(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []model.StockCountWithDetails
	for rows.Next() {
		var result model.StockCountWithDetails
		err := rows.Scan(
			&result.ID,
			&result.WarehouseID,
			&result.CountType,
			&result.Status,
			&result.ScheduledDate,
			&result.CompletedDate,
			&result.CreatedAt,
			&result.UpdatedAt,
			&result.DeletedAt,
			&result.Warehouse.ID,
			&result.Warehouse.Name,
			&result.Warehouse.Code,
			&result.Warehouse.Type,
			&result.Warehouse.Category,
			&result.Warehouse.Description,
			&result.Warehouse.Address,
			&result.Warehouse.IsActive,
			&result.Warehouse.IsSystemWarehouse,
			&result.Warehouse.CreatedAt,
			&result.Warehouse.UpdatedAt,
			&result.Warehouse.DeletedAt,
		)
		if err != nil {
			return nil, err
		}

		// Get lines for this stock count
		lines, err := s.GetLinesByStockCountID(ctx, result.ID)
		if err != nil {
			return nil, err
		}
		result.Lines = lines

		results = append(results, result)
	}

	return results, nil
}

// GetByWarehouseID implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) GetByWarehouseID(ctx context.Context, warehouseID string) ([]model.StockCountWithDetails, error) {
	query := `
		SELECT
			sc.id, sc.warehouse_id, sc.count_type, sc.status, sc.scheduled_date, sc.completed_date,
			sc.created_at, sc.updated_at, sc.deleted_at,
			w.id, w.name, w.code, w.type, w.category, w.description, w.address, w.is_active, w.is_system_warehouse,
			w.created_at, w.updated_at, w.deleted_at
		FROM stock_counts sc
		LEFT JOIN warehouses w ON sc.warehouse_id = w.id
		WHERE sc.warehouse_id = $1 AND sc.deleted_at IS NULL
		ORDER BY sc.created_at DESC
	`

	rows, err := s.pool.Query(ctx, query, warehouseID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []model.StockCountWithDetails
	for rows.Next() {
		var result model.StockCountWithDetails
		err := rows.Scan(
			&result.ID,
			&result.WarehouseID,
			&result.CountType,
			&result.Status,
			&result.ScheduledDate,
			&result.CompletedDate,
			&result.CreatedAt,
			&result.UpdatedAt,
			&result.DeletedAt,
			&result.Warehouse.ID,
			&result.Warehouse.Name,
			&result.Warehouse.Code,
			&result.Warehouse.Type,
			&result.Warehouse.Category,
			&result.Warehouse.Description,
			&result.Warehouse.Address,
			&result.Warehouse.IsActive,
			&result.Warehouse.IsSystemWarehouse,
			&result.Warehouse.CreatedAt,
			&result.Warehouse.UpdatedAt,
			&result.Warehouse.DeletedAt,
		)
		if err != nil {
			return nil, err
		}

		// Get lines for this stock count
		lines, err := s.GetLinesByStockCountID(ctx, result.ID)
		if err != nil {
			return nil, err
		}
		result.Lines = lines

		results = append(results, result)
	}

	return results, nil
}

// GetByStatus implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) GetByStatus(ctx context.Context, status string) ([]model.StockCountWithDetails, error) {
	query := `
		SELECT
			sc.id, sc.warehouse_id, sc.count_type, sc.status, sc.scheduled_date, sc.completed_date,
			sc.created_at, sc.updated_at, sc.deleted_at,
			w.id, w.name, w.code, w.type, w.category, w.description, w.address, w.is_active, w.is_system_warehouse,
			w.created_at, w.updated_at, w.deleted_at
		FROM stock_counts sc
		LEFT JOIN warehouses w ON sc.warehouse_id = w.id
		WHERE sc.status = $1 AND sc.deleted_at IS NULL
		ORDER BY sc.created_at DESC
	`

	rows, err := s.pool.Query(ctx, query, status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []model.StockCountWithDetails
	for rows.Next() {
		var result model.StockCountWithDetails
		err := rows.Scan(
			&result.ID,
			&result.WarehouseID,
			&result.CountType,
			&result.Status,
			&result.ScheduledDate,
			&result.CompletedDate,
			&result.CreatedAt,
			&result.UpdatedAt,
			&result.DeletedAt,
			&result.Warehouse.ID,
			&result.Warehouse.Name,
			&result.Warehouse.Code,
			&result.Warehouse.Type,
			&result.Warehouse.Category,
			&result.Warehouse.Description,
			&result.Warehouse.Address,
			&result.Warehouse.IsActive,
			&result.Warehouse.IsSystemWarehouse,
			&result.Warehouse.CreatedAt,
			&result.Warehouse.UpdatedAt,
			&result.Warehouse.DeletedAt,
		)
		if err != nil {
			return nil, err
		}

		// Get lines for this stock count
		lines, err := s.GetLinesByStockCountID(ctx, result.ID)
		if err != nil {
			return nil, err
		}
		result.Lines = lines

		results = append(results, result)
	}

	return results, nil
}

// GetByCountType implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) GetByCountType(ctx context.Context, countType string) ([]model.StockCountWithDetails, error) {
	query := `
		SELECT
			sc.id, sc.warehouse_id, sc.count_type, sc.status, sc.scheduled_date, sc.completed_date,
			sc.created_at, sc.updated_at, sc.deleted_at,
			w.id, w.name, w.code, w.type, w.category, w.description, w.address, w.is_active, w.is_system_warehouse,
			w.created_at, w.updated_at, w.deleted_at
		FROM stock_counts sc
		LEFT JOIN warehouses w ON sc.warehouse_id = w.id
		WHERE sc.count_type = $1 AND sc.deleted_at IS NULL
		ORDER BY sc.created_at DESC
	`

	rows, err := s.pool.Query(ctx, query, countType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []model.StockCountWithDetails
	for rows.Next() {
		var result model.StockCountWithDetails
		err := rows.Scan(
			&result.ID,
			&result.WarehouseID,
			&result.CountType,
			&result.Status,
			&result.ScheduledDate,
			&result.CompletedDate,
			&result.CreatedAt,
			&result.UpdatedAt,
			&result.DeletedAt,
			&result.Warehouse.ID,
			&result.Warehouse.Name,
			&result.Warehouse.Code,
			&result.Warehouse.Type,
			&result.Warehouse.Category,
			&result.Warehouse.Description,
			&result.Warehouse.Address,
			&result.Warehouse.IsActive,
			&result.Warehouse.IsSystemWarehouse,
			&result.Warehouse.CreatedAt,
			&result.Warehouse.UpdatedAt,
			&result.Warehouse.DeletedAt,
		)
		if err != nil {
			return nil, err
		}

		// Get lines for this stock count
		lines, err := s.GetLinesByStockCountID(ctx, result.ID)
		if err != nil {
			return nil, err
		}
		result.Lines = lines

		results = append(results, result)
	}

	return results, nil
}

// Delete implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) Delete(ctx context.Context, id string) error {
	query := `UPDATE stock_counts SET deleted_at = NOW() WHERE id = $1`
	_, err := s.pool.Exec(ctx, query, id)
	return err
}

// CountByProp implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	query := `SELECT COUNT(*) FROM stock_counts WHERE ` + prop + ` = $1 AND deleted_at IS NULL`
	var count int
	err := s.pool.QueryRow(ctx, query, value).Scan(&count)
	return count, err
}

// CreateLine implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) CreateLine(ctx context.Context, line model.StockCountLine) error {
	query := `
		INSERT INTO stock_count_lines (id, stock_count_id, inventory_id, system_quantity, counted_quantity, variance, counted_by_user_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
	`
	_, err := s.pool.Exec(ctx, query,
		line.ID,
		line.StockCountID,
		line.InventoryID,
		line.SystemQuantity,
		line.CountedQuantity,
		line.Variance,
		line.CountedByUserID,
	)
	return err
}

// UpdateLine implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) UpdateLine(ctx context.Context, line model.StockCountLine) error {
	query := `
		UPDATE stock_count_lines
		SET counted_quantity = $2, variance = $3, counted_by_user_id = $4, updated_at = NOW()
		WHERE id = $1 AND deleted_at IS NULL
	`
	_, err := s.pool.Exec(ctx, query,
		line.ID,
		line.CountedQuantity,
		line.Variance,
		line.CountedByUserID,
	)
	return err
}

// GetLineByProp implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) GetLineByProp(ctx context.Context, prop string, value string) (*model.StockCountLine, error) {
	query := `
		SELECT id, stock_count_id, inventory_id, system_quantity, counted_quantity, variance, counted_by_user_id, created_at, updated_at, deleted_at
		FROM stock_count_lines
		WHERE ` + prop + ` = $1 AND deleted_at IS NULL
	`

	var line model.StockCountLine
	err := s.pool.QueryRow(ctx, query, value).Scan(
		&line.ID,
		&line.StockCountID,
		&line.InventoryID,
		&line.SystemQuantity,
		&line.CountedQuantity,
		&line.Variance,
		&line.CountedByUserID,
		&line.CreatedAt,
		&line.UpdatedAt,
		&line.DeletedAt,
	)
	if err != nil {
		return nil, err
	}

	return &line, nil
}

// GetLineByPropWithDetails implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) GetLineByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockCountLineWithDetails, error) {
	// This is a complex query that would need to join with inventory and user tables
	// For now, let's implement a basic version and get the details separately
	line, err := s.GetLineByProp(ctx, prop, value)
	if err != nil {
		return nil, err
	}

	// TODO: Implement full details query with joins to inventory and user tables
	result := &model.StockCountLineWithDetails{
		ID:              line.ID,
		StockCountID:    line.StockCountID,
		InventoryID:     line.InventoryID,
		SystemQuantity:  line.SystemQuantity,
		CountedQuantity: line.CountedQuantity,
		Variance:        line.Variance,
		CountedByUserID: line.CountedByUserID,
		CreatedAt:       line.CreatedAt,
		UpdatedAt:       line.UpdatedAt,
		DeletedAt:       line.DeletedAt,
	}

	return result, nil
}

// GetLinesByStockCountID implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) GetLinesByStockCountID(ctx context.Context, stockCountID string) ([]model.StockCountLineWithDetails, error) {
	query := `
		SELECT id, stock_count_id, inventory_id, system_quantity, counted_quantity, variance, counted_by_user_id, created_at, updated_at, deleted_at
		FROM stock_count_lines
		WHERE stock_count_id = $1 AND deleted_at IS NULL
		ORDER BY created_at ASC
	`

	rows, err := s.pool.Query(ctx, query, stockCountID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []model.StockCountLineWithDetails
	for rows.Next() {
		var line model.StockCountLine
		err := rows.Scan(
			&line.ID,
			&line.StockCountID,
			&line.InventoryID,
			&line.SystemQuantity,
			&line.CountedQuantity,
			&line.Variance,
			&line.CountedByUserID,
			&line.CreatedAt,
			&line.UpdatedAt,
			&line.DeletedAt,
		)
		if err != nil {
			return nil, err
		}

		// Convert to WithDetails struct
		result := model.StockCountLineWithDetails{
			ID:              line.ID,
			StockCountID:    line.StockCountID,
			InventoryID:     line.InventoryID,
			SystemQuantity:  line.SystemQuantity,
			CountedQuantity: line.CountedQuantity,
			Variance:        line.Variance,
			CountedByUserID: line.CountedByUserID,
			CreatedAt:       line.CreatedAt,
			UpdatedAt:       line.UpdatedAt,
			DeletedAt:       line.DeletedAt,
		}

		results = append(results, result)
	}

	return results, nil
}

// GetLinesByInventoryID implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) GetLinesByInventoryID(ctx context.Context, inventoryID string) ([]model.StockCountLineWithDetails, error) {
	query := `
		SELECT id, stock_count_id, inventory_id, system_quantity, counted_quantity, variance, counted_by_user_id, created_at, updated_at, deleted_at
		FROM stock_count_lines
		WHERE inventory_id = $1 AND deleted_at IS NULL
		ORDER BY created_at DESC
	`

	rows, err := s.pool.Query(ctx, query, inventoryID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []model.StockCountLineWithDetails
	for rows.Next() {
		var line model.StockCountLine
		err := rows.Scan(
			&line.ID,
			&line.StockCountID,
			&line.InventoryID,
			&line.SystemQuantity,
			&line.CountedQuantity,
			&line.Variance,
			&line.CountedByUserID,
			&line.CreatedAt,
			&line.UpdatedAt,
			&line.DeletedAt,
		)
		if err != nil {
			return nil, err
		}

		// Convert to WithDetails struct
		result := model.StockCountLineWithDetails{
			ID:              line.ID,
			StockCountID:    line.StockCountID,
			InventoryID:     line.InventoryID,
			SystemQuantity:  line.SystemQuantity,
			CountedQuantity: line.CountedQuantity,
			Variance:        line.Variance,
			CountedByUserID: line.CountedByUserID,
			CreatedAt:       line.CreatedAt,
			UpdatedAt:       line.UpdatedAt,
			DeletedAt:       line.DeletedAt,
		}

		results = append(results, result)
	}

	return results, nil
}

// DeleteLine implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) DeleteLine(ctx context.Context, id string) error {
	query := `UPDATE stock_count_lines SET deleted_at = NOW() WHERE id = $1`
	_, err := s.pool.Exec(ctx, query, id)
	return err
}

// CountLinesByProp implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) CountLinesByProp(ctx context.Context, prop string, value string) (int, error) {
	query := `SELECT COUNT(*) FROM stock_count_lines WHERE ` + prop + ` = $1 AND deleted_at IS NULL`
	var count int
	err := s.pool.QueryRow(ctx, query, value).Scan(&count)
	return count, err
}

// CountLinesByStockCountInventory implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) CountLinesByStockCountInventory(ctx context.Context, stockCountID, inventoryID string) (int, error) {
	query := `SELECT COUNT(*) FROM stock_count_lines WHERE stock_count_id = $1 AND inventory_id = $2 AND deleted_at IS NULL`
	var count int
	err := s.pool.QueryRow(ctx, query, stockCountID, inventoryID).Scan(&count)
	return count, err
}

// CreateLinesForStockCount implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) CreateLinesForStockCount(ctx context.Context, stockCountID string, inventoryIDs []string) error {
	if len(inventoryIDs) == 0 {
		return nil
	}

	// Build batch insert query
	query := `
		INSERT INTO stock_count_lines (id, stock_count_id, inventory_id, system_quantity, created_at, updated_at)
		SELECT
			gen_random_uuid()::text,
			$1,
			i.id,
			i.current_stock,
			NOW(),
			NOW()
		FROM inventory i
		WHERE i.id = ANY($2) AND i.deleted_at IS NULL
	`

	_, err := s.pool.Exec(ctx, query, stockCountID, inventoryIDs)
	return err
}

// UpdateStockCountStatus implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) UpdateStockCountStatus(ctx context.Context, stockCountID string, status string, completedDate *time.Time) error {
	query := `
		UPDATE stock_counts
		SET status = $2, completed_date = $3, updated_at = NOW()
		WHERE id = $1 AND deleted_at IS NULL
	`
	_, err := s.pool.Exec(ctx, query, stockCountID, status, completedDate)
	return err
}

// CalculateVariances implements StockCountPostgreRepo.
func (s *stockCountPostgreRepo) CalculateVariances(ctx context.Context, stockCountID string) error {
	query := `
		UPDATE stock_count_lines
		SET variance = COALESCE(counted_quantity, 0) - system_quantity,
			updated_at = NOW()
		WHERE stock_count_id = $1 AND counted_quantity IS NOT NULL AND deleted_at IS NULL
	`
	_, err := s.pool.Exec(ctx, query, stockCountID)
	return err
}
