-- Stock Counts table
CREATE TABLE dev.stock_counts (
    id VARCHAR(255) PRIMARY KEY,
    warehouse_id VARCHAR(255) NOT NULL,
    count_type VARCHAR(50) NOT NULL CHECK (count_type IN ('FULL', 'CYCLE', 'SPOT')),
    status VARCHAR(50) NOT NULL CHECK (status IN ('PLANNED', 'IN_PROGRESS', 'COMPLETED')),
    scheduled_date TIMESTAMPTZ,
    completed_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (warehouse_id) REFERENCES dev.warehouses(id)
);

-- Stock Count Lines table
CREATE TABLE dev.stock_count_lines (
    id VARCHAR(255) PRIMARY KEY,
    stock_count_id VARCHAR(255) NOT NULL,
    inventory_id VARCHAR(255) NOT NULL,
    system_quantity DECIMAL(10,2) NOT NULL DEFAULT 0,
    counted_quantity DECIMAL(10,2),
    variance DECIMAL(10,2),
    counted_by_user_id VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (stock_count_id) REFERENCES dev.stock_counts(id) ON DELETE CASCADE,
    FOREIGN KEY (inventory_id) REFERENCES dev.inventory(id),
    FOREIGN KEY (counted_by_user_id) REFERENCES dev.users(id),
    UNIQUE(stock_count_id, inventory_id)
);

-- Indexes for better performance
CREATE INDEX idx_stock_counts_warehouse_id ON dev.stock_counts(warehouse_id);
CREATE INDEX idx_stock_counts_status ON dev.stock_counts(status);
CREATE INDEX idx_stock_counts_count_type ON dev.stock_counts(count_type);
CREATE INDEX idx_stock_counts_scheduled_date ON dev.stock_counts(scheduled_date);
CREATE INDEX idx_stock_counts_deleted_at ON dev.stock_counts(deleted_at);

CREATE INDEX idx_stock_count_lines_stock_count_id ON dev.stock_count_lines(stock_count_id);
CREATE INDEX idx_stock_count_lines_inventory_id ON dev.stock_count_lines(inventory_id);
CREATE INDEX idx_stock_count_lines_counted_by_user_id ON dev.stock_count_lines(counted_by_user_id);
CREATE INDEX idx_stock_count_lines_deleted_at ON dev.stock_count_lines(deleted_at);

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION dev.update_stock_counts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_stock_counts_updated_at
    BEFORE UPDATE ON dev.stock_counts
    FOR EACH ROW
    EXECUTE FUNCTION dev.update_stock_counts_updated_at();

CREATE OR REPLACE FUNCTION dev.update_stock_count_lines_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_stock_count_lines_updated_at
    BEFORE UPDATE ON dev.stock_count_lines
    FOR EACH ROW
    EXECUTE FUNCTION dev.update_stock_count_lines_updated_at();
