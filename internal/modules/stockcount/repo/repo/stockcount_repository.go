package repo

import (
	"context"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/repo/pg"
)

type stockCountRepository struct {
	pgRepo pg.StockCountPostgreRepo
}

// Create implements model.StockCountRepository.
func (s *stockCountRepository) Create(ctx context.Context, stockCount model.StockCount) error {
	return s.pgRepo.Create(ctx, stockCount)
}

// Update implements model.StockCountRepository.
func (s *stockCountRepository) Update(ctx context.Context, stockCount model.StockCount) error {
	return s.pgRepo.Update(ctx, stockCount)
}

// GetByProp implements model.StockCountRepository.
func (s *stockCountRepository) GetByProp(ctx context.Context, prop string, value string) (*model.StockCount, error) {
	return s.pgRepo.GetByProp(ctx, prop, value)
}

// GetByPropWithDetails implements model.StockCountRepository.
func (s *stockCountRepository) GetByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockCountWithDetails, error) {
	return s.pgRepo.GetByPropWithDetails(ctx, prop, value)
}

// GetAll implements model.StockCountRepository.
func (s *stockCountRepository) GetAll(ctx context.Context) ([]model.StockCountWithDetails, error) {
	return s.pgRepo.GetAll(ctx)
}

// GetByWarehouseID implements model.StockCountRepository.
func (s *stockCountRepository) GetByWarehouseID(ctx context.Context, warehouseID string) ([]model.StockCountWithDetails, error) {
	return s.pgRepo.GetByWarehouseID(ctx, warehouseID)
}

// GetByStatus implements model.StockCountRepository.
func (s *stockCountRepository) GetByStatus(ctx context.Context, status string) ([]model.StockCountWithDetails, error) {
	return s.pgRepo.GetByStatus(ctx, status)
}

// GetByCountType implements model.StockCountRepository.
func (s *stockCountRepository) GetByCountType(ctx context.Context, countType string) ([]model.StockCountWithDetails, error) {
	return s.pgRepo.GetByCountType(ctx, countType)
}

// Delete implements model.StockCountRepository.
func (s *stockCountRepository) Delete(ctx context.Context, id string) error {
	return s.pgRepo.Delete(ctx, id)
}

// CountByProp implements model.StockCountRepository.
func (s *stockCountRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return s.pgRepo.CountByProp(ctx, prop, value)
}

// CreateLine implements model.StockCountRepository.
func (s *stockCountRepository) CreateLine(ctx context.Context, line model.StockCountLine) error {
	return s.pgRepo.CreateLine(ctx, line)
}

// UpdateLine implements model.StockCountRepository.
func (s *stockCountRepository) UpdateLine(ctx context.Context, line model.StockCountLine) error {
	return s.pgRepo.UpdateLine(ctx, line)
}

// GetLineByProp implements model.StockCountRepository.
func (s *stockCountRepository) GetLineByProp(ctx context.Context, prop string, value string) (*model.StockCountLine, error) {
	return s.pgRepo.GetLineByProp(ctx, prop, value)
}

// GetLineByPropWithDetails implements model.StockCountRepository.
func (s *stockCountRepository) GetLineByPropWithDetails(ctx context.Context, prop string, value string) (*model.StockCountLineWithDetails, error) {
	return s.pgRepo.GetLineByPropWithDetails(ctx, prop, value)
}

// GetLinesByStockCountID implements model.StockCountRepository.
func (s *stockCountRepository) GetLinesByStockCountID(ctx context.Context, stockCountID string) ([]model.StockCountLineWithDetails, error) {
	return s.pgRepo.GetLinesByStockCountID(ctx, stockCountID)
}

// GetLinesByInventoryID implements model.StockCountRepository.
func (s *stockCountRepository) GetLinesByInventoryID(ctx context.Context, inventoryID string) ([]model.StockCountLineWithDetails, error) {
	return s.pgRepo.GetLinesByInventoryID(ctx, inventoryID)
}

// DeleteLine implements model.StockCountRepository.
func (s *stockCountRepository) DeleteLine(ctx context.Context, id string) error {
	return s.pgRepo.DeleteLine(ctx, id)
}

// CountLinesByProp implements model.StockCountRepository.
func (s *stockCountRepository) CountLinesByProp(ctx context.Context, prop string, value string) (int, error) {
	return s.pgRepo.CountLinesByProp(ctx, prop, value)
}

// CountLinesByStockCountInventory implements model.StockCountRepository.
func (s *stockCountRepository) CountLinesByStockCountInventory(ctx context.Context, stockCountID, inventoryID string) (int, error) {
	return s.pgRepo.CountLinesByStockCountInventory(ctx, stockCountID, inventoryID)
}

// CreateLinesForStockCount implements model.StockCountRepository.
func (s *stockCountRepository) CreateLinesForStockCount(ctx context.Context, stockCountID string, inventoryIDs []string) error {
	return s.pgRepo.CreateLinesForStockCount(ctx, stockCountID, inventoryIDs)
}

// UpdateStockCountStatus implements model.StockCountRepository.
func (s *stockCountRepository) UpdateStockCountStatus(ctx context.Context, stockCountID string, status string, completedDate *time.Time) error {
	return s.pgRepo.UpdateStockCountStatus(ctx, stockCountID, status, completedDate)
}

// CalculateVariances implements model.StockCountRepository.
func (s *stockCountRepository) CalculateVariances(ctx context.Context, stockCountID string) error {
	return s.pgRepo.CalculateVariances(ctx, stockCountID)
}

func NewStockCountRepository(pgRepo pg.StockCountPostgreRepo) model.StockCountRepository {
	return &stockCountRepository{
		pgRepo: pgRepo,
	}
}
