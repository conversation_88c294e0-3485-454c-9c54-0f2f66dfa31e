package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

var ErrorHandlers = rest.RespErrHandlers{
	model.StockCountConflictCode:            http.StatusConflict,
	model.StockCountNotFoundCode:            http.StatusNotFound,
	model.StockCountInvalidStatusCode:       http.StatusBadRequest,
	model.StockCountInvalidCountTypeCode:    http.StatusBadRequest,
	model.StockCountAlreadyStartedCode:      http.StatusConflict,
	model.StockCountAlreadyCompletedCode:    http.StatusConflict,
	model.StockCountNotStartedCode:          http.StatusBadRequest,
	model.StockCountLineConflictCode:        http.StatusConflict,
	model.StockCountLineNotFoundCode:        http.StatusNotFound,
	model.StockCountLineInvalidQuantityCode: http.StatusBadRequest,
	model.StockCountLineAlreadyCountedCode:  http.StatusConflict,
	model.StockCountInvalidWarehouseCode:    http.StatusBadRequest,
	model.StockCountInvalidInventoryCode:    http.StatusBadRequest,
	model.StockCountInvalidUserCode:         http.StatusBadRequest,
}

func respErrHandler(w http.ResponseWriter, r *http.Request, err error, message string) {
	rest.RespErrHandler(w, r, err, message, ErrorHandlers)
}
