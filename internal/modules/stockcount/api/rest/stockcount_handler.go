package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type StockCountHandler interface {
	// StockCount operations
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	GetByWarehouseID(w http.ResponseWriter, r *http.Request)
	GetByStatus(w http.ResponseWriter, r *http.Request)
	GetByCountType(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)

	// StockCountLine operations
	CreateLine(w http.ResponseWriter, r *http.Request)
	UpdateLine(w http.ResponseWriter, r *http.Request)
	GetLineById(w http.ResponseWriter, r *http.Request)
	GetLinesByStockCountID(w http.ResponseWriter, r *http.Request)
	GetLinesByInventoryID(w http.ResponseWriter, r *http.Request)
	DeleteLine(w http.ResponseWriter, r *http.Request)

	// Business operations
	StartStockCount(w http.ResponseWriter, r *http.Request)
	CompleteStockCount(w http.ResponseWriter, r *http.Request)
	GenerateLinesForWarehouse(w http.ResponseWriter, r *http.Request)
	UpdateLineCount(w http.ResponseWriter, r *http.Request)
	CalculateVariances(w http.ResponseWriter, r *http.Request)
}

type stockCountHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.StockCountUsecase
}

func NewStockCountHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.StockCountUsecase,
) StockCountHandler {
	return &stockCountHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
