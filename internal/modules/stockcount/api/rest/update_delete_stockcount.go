package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type stockCountUpdate struct {
	ID            string     `json:"id" validate:"required"`
	WarehouseID   string     `json:"warehouse_id" validate:"required"`
	CountType     string     `json:"count_type" validate:"required"`
	Status        string     `json:"status" validate:"required"`
	ScheduledDate *time.Time `json:"scheduled_date"`
	CompletedDate *time.Time `json:"completed_date"`
}

type stockCountLineUpdate struct {
	ID              string   `json:"id" validate:"required"`
	CountedQuantity *float64 `json:"counted_quantity"`
	CountedByUserID *string  `json:"counted_by_user_id"`
}

func stockCountUpdateToModel(req stockCountUpdate) model.StockCountUpdate {
	return model.StockCountUpdate{
		ID:            req.ID,
		WarehouseID:   req.WarehouseID,
		CountType:     req.CountType,
		Status:        req.Status,
		ScheduledDate: req.ScheduledDate,
		CompletedDate: req.CompletedDate,
	}
}

func stockCountLineUpdateToModel(req stockCountLineUpdate) model.StockCountLineUpdate {
	return model.StockCountLineUpdate{
		ID:              req.ID,
		CountedQuantity: req.CountedQuantity,
		CountedByUserID: req.CountedByUserID,
	}
}

// Update implements StockCountHandler.
func (s *stockCountHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[stockCountUpdate](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	err = s.useCase.Update(ctx, stockCountUpdateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to update stock count")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

// UpdateLine implements StockCountHandler.
func (s *stockCountHandler) UpdateLine(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[stockCountLineUpdate](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	err = s.useCase.UpdateLine(ctx, stockCountLineUpdateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to update stock count line")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

// Delete implements StockCountHandler.
func (s *stockCountHandler) Delete(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := s.useCase.Delete(ctx, id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to delete stock count")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

// DeleteLine implements StockCountHandler.
func (s *stockCountHandler) DeleteLine(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := s.useCase.DeleteLine(ctx, id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to delete stock count line")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
