package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type updateLineCountRequest struct {
	CountedQuantity float64 `json:"counted_quantity" validate:"min=0"`
	CountedByUserID string  `json:"counted_by_user_id" validate:"required"`
}

// StartStockCount implements StockCountHandler.
func (s *stockCountHandler) StartStockCount(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := s.useCase.StartStockCount(ctx, id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to start stock count")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

// CompleteStockCount implements StockCountHandler.
func (s *stockCountHandler) CompleteStockCount(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := s.useCase.CompleteStockCount(ctx, id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to complete stock count")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

// GenerateLinesForWarehouse implements StockCountHandler.
func (s *stockCountHandler) GenerateLinesForWarehouse(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := s.useCase.GenerateLinesForWarehouse(ctx, id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to generate stock count lines")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

// UpdateLineCount implements StockCountHandler.
func (s *stockCountHandler) UpdateLineCount(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	req, err := rest.DecodeAndValidate[updateLineCountRequest](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	err = s.useCase.UpdateLineCount(ctx, id, req.CountedQuantity, req.CountedByUserID)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to update line count")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}

// CalculateVariances implements StockCountHandler.
func (s *stockCountHandler) CalculateVariances(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	err := s.useCase.CalculateVariances(ctx, id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to calculate variances")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
