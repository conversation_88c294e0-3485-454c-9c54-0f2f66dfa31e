package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type stockCountCreate struct {
	WarehouseID   string     `json:"warehouse_id" validate:"required"`
	CountType     string     `json:"count_type" validate:"required"`
	Status        string     `json:"status" validate:"required"`
	ScheduledDate *time.Time `json:"scheduled_date"`
}

type stockCountLineCreate struct {
	StockCountID     string   `json:"stock_count_id" validate:"required"`
	InventoryID      string   `json:"inventory_id" validate:"required"`
	SystemQuantity   float64  `json:"system_quantity" validate:"min=0"`
	CountedQuantity  *float64 `json:"counted_quantity"`
	CountedByUserID  *string  `json:"counted_by_user_id"`
}

func stockCountCreateToModel(req stockCountCreate) model.StockCountCreate {
	return model.StockCountCreate{
		WarehouseID:   req.WarehouseID,
		CountType:     req.CountType,
		Status:        req.Status,
		ScheduledDate: req.ScheduledDate,
	}
}

func stockCountLineCreateToModel(req stockCountLineCreate) model.StockCountLineCreate {
	return model.StockCountLineCreate{
		StockCountID:     req.StockCountID,
		InventoryID:      req.InventoryID,
		SystemQuantity:   req.SystemQuantity,
		CountedQuantity:  req.CountedQuantity,
		CountedByUserID:  req.CountedByUserID,
	}
}

// Create implements StockCountHandler.
func (s *stockCountHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[stockCountCreate](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	id, err := s.useCase.Create(ctx, stockCountCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to create stock count")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}

// CreateLine implements StockCountHandler.
func (s *stockCountHandler) CreateLine(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[stockCountLineCreate](w, r, s.validator)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		return
	}

	id, err := s.useCase.CreateLine(ctx, stockCountLineCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to create stock count line")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
