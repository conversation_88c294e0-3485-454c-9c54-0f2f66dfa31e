package rest

import (
	"net/http"
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/stockcount/model"
	warehouseModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/warehouse/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type stockCountResult struct {
	ID            string                 `json:"id"`
	WarehouseID   string                 `json:"warehouse_id"`
	CountType     string                 `json:"count_type"`
	Status        string                 `json:"status"`
	ScheduledDate *time.Time             `json:"scheduled_date"`
	CompletedDate *time.Time             `json:"completed_date"`
	CreatedAt     *time.Time             `json:"created_at"`
	UpdatedAt     *time.Time             `json:"updated_at"`
	Warehouse     *warehouseResult       `json:"warehouse,omitempty"`
	Lines         []stockCountLineResult `json:"lines,omitempty"`
}

type stockCountLineResult struct {
	ID              string     `json:"id"`
	StockCountID    string     `json:"stock_count_id"`
	InventoryID     string     `json:"inventory_id"`
	SystemQuantity  float64    `json:"system_quantity"`
	CountedQuantity *float64   `json:"counted_quantity"`
	Variance        *float64   `json:"variance"`
	CountedByUserID *string    `json:"counted_by_user_id"`
	CreatedAt       *time.Time `json:"created_at"`
	UpdatedAt       *time.Time `json:"updated_at"`
}

type warehouseResult struct {
	ID                string     `json:"id"`
	Name              string     `json:"name"`
	Code              string     `json:"code"`
	Type              string     `json:"type"`
	Category          string     `json:"category"`
	Description       *string    `json:"description"`
	Address           *string    `json:"address"`
	IsActive          bool       `json:"is_active"`
	IsSystemWarehouse bool       `json:"is_system_warehouse"`
	CreatedAt         *time.Time `json:"created_at"`
	UpdatedAt         *time.Time `json:"updated_at"`
}

func stockCountToResult(sc *model.StockCount) stockCountResult {
	return stockCountResult{
		ID:            sc.ID,
		WarehouseID:   sc.WarehouseID,
		CountType:     sc.CountType,
		Status:        sc.Status,
		ScheduledDate: sc.ScheduledDate,
		CompletedDate: sc.CompletedDate,
		CreatedAt:     sc.CreatedAt,
		UpdatedAt:     sc.UpdatedAt,
	}
}

func stockCountWithDetailsToResult(sc *model.StockCountWithDetails) stockCountResult {
	result := stockCountResult{
		ID:            sc.ID,
		WarehouseID:   sc.WarehouseID,
		CountType:     sc.CountType,
		Status:        sc.Status,
		ScheduledDate: sc.ScheduledDate,
		CompletedDate: sc.CompletedDate,
		CreatedAt:     sc.CreatedAt,
		UpdatedAt:     sc.UpdatedAt,
		Warehouse:     warehouseToResult(&sc.Warehouse),
	}

	// Convert lines
	if len(sc.Lines) > 0 {
		result.Lines = make([]stockCountLineResult, len(sc.Lines))
		for i, line := range sc.Lines {
			result.Lines[i] = stockCountLineWithDetailsToResult(&line)
		}
	}

	return result
}

func stockCountLineToResult(line *model.StockCountLine) stockCountLineResult {
	return stockCountLineResult{
		ID:              line.ID,
		StockCountID:    line.StockCountID,
		InventoryID:     line.InventoryID,
		SystemQuantity:  line.SystemQuantity,
		CountedQuantity: line.CountedQuantity,
		Variance:        line.Variance,
		CountedByUserID: line.CountedByUserID,
		CreatedAt:       line.CreatedAt,
		UpdatedAt:       line.UpdatedAt,
	}
}

func stockCountLineWithDetailsToResult(line *model.StockCountLineWithDetails) stockCountLineResult {
	return stockCountLineResult{
		ID:              line.ID,
		StockCountID:    line.StockCountID,
		InventoryID:     line.InventoryID,
		SystemQuantity:  line.SystemQuantity,
		CountedQuantity: line.CountedQuantity,
		Variance:        line.Variance,
		CountedByUserID: line.CountedByUserID,
		CreatedAt:       line.CreatedAt,
		UpdatedAt:       line.UpdatedAt,
	}
}

func warehouseToResult(w *warehouseModel.Warehouse) *warehouseResult {
	return &warehouseResult{
		ID:                w.ID,
		Name:              w.Name,
		Code:              w.Code,
		Type:              w.Type,
		Category:          w.Category,
		Description:       w.Description,
		Address:           w.Address,
		IsActive:          w.IsActive,
		IsSystemWarehouse: w.IsSystemWarehouse,
		CreatedAt:         w.CreatedAt,
		UpdatedAt:         w.UpdatedAt,
	}
}

// GetById implements StockCountHandler.
func (s *stockCountHandler) GetById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	stockCount, err := s.useCase.GetByPropWithDetails(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock count")
		return
	}

	result := stockCountWithDetailsToResult(stockCount)
	rest.SuccessDResponse(w, r, result, http.StatusOK)
}

// GetAll implements StockCountHandler.
func (s *stockCountHandler) GetAll(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	stockCounts, err := s.useCase.GetAll(ctx)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock counts")
		return
	}

	results := make([]stockCountResult, len(stockCounts))
	for i, sc := range stockCounts {
		results[i] = stockCountWithDetailsToResult(&sc)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetByWarehouseID implements StockCountHandler.
func (s *stockCountHandler) GetByWarehouseID(w http.ResponseWriter, r *http.Request) {
	warehouseID := r.PathValue("warehouse_id")
	ctx := r.Context()

	if warehouseID == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Warehouse ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	stockCounts, err := s.useCase.GetByWarehouseID(ctx, warehouseID)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock counts by warehouse")
		return
	}

	results := make([]stockCountResult, len(stockCounts))
	for i, sc := range stockCounts {
		results[i] = stockCountWithDetailsToResult(&sc)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetByStatus implements StockCountHandler.
func (s *stockCountHandler) GetByStatus(w http.ResponseWriter, r *http.Request) {
	status := r.PathValue("status")
	ctx := r.Context()

	if status == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Status parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	stockCounts, err := s.useCase.GetByStatus(ctx, status)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock counts by status")
		return
	}

	results := make([]stockCountResult, len(stockCounts))
	for i, sc := range stockCounts {
		results[i] = stockCountWithDetailsToResult(&sc)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetByCountType implements StockCountHandler.
func (s *stockCountHandler) GetByCountType(w http.ResponseWriter, r *http.Request) {
	countType := r.PathValue("count_type")
	ctx := r.Context()

	if countType == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Count type parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	stockCounts, err := s.useCase.GetByCountType(ctx, countType)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock counts by count type")
		return
	}

	results := make([]stockCountResult, len(stockCounts))
	for i, sc := range stockCounts {
		results[i] = stockCountWithDetailsToResult(&sc)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetLineById implements StockCountHandler.
func (s *stockCountHandler) GetLineById(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	if id == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	line, err := s.useCase.GetLineByPropWithDetails(ctx, "id", id)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock count line")
		return
	}

	result := stockCountLineWithDetailsToResult(line)
	rest.SuccessDResponse(w, r, result, http.StatusOK)
}

// GetLinesByStockCountID implements StockCountHandler.
func (s *stockCountHandler) GetLinesByStockCountID(w http.ResponseWriter, r *http.Request) {
	stockCountID := r.PathValue("stock_count_id")
	ctx := r.Context()

	if stockCountID == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Stock count ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	lines, err := s.useCase.GetLinesByStockCountID(ctx, stockCountID)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock count lines")
		return
	}

	results := make([]stockCountLineResult, len(lines))
	for i, line := range lines {
		results[i] = stockCountLineWithDetailsToResult(&line)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}

// GetLinesByInventoryID implements StockCountHandler.
func (s *stockCountHandler) GetLinesByInventoryID(w http.ResponseWriter, r *http.Request) {
	inventoryID := r.PathValue("inventory_id")
	ctx := r.Context()

	if inventoryID == "" {
		rest.ErrorResponse(w, r, utils.BadRequestf("Inventory ID parameter is required", nil, nil), http.StatusBadRequest)
		return
	}

	lines, err := s.useCase.GetLinesByInventoryID(ctx, inventoryID)
	if err != nil {
		utils.LogErr(ctx, s.log, err)
		respErrHandler(w, r, err, "Failed to get stock count lines by inventory")
		return
	}

	results := make([]stockCountLineResult, len(lines))
	for i, line := range lines {
		results[i] = stockCountLineWithDetailsToResult(&line)
	}

	rest.SuccessDResponse(w, r, results, http.StatusOK)
}
